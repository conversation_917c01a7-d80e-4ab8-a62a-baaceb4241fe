/**
 * @file concurrent_strategy.ixx
 * @brief 并发初始化策略实现
 *
 * 实现了使用modula.generated的并发初始化策略，
 * 将生成的并发初始化逻辑与ModuleManager框架连接。
 */

module;

#include <iostream>

export module concurrent_strategy;

// 导入modula框架
import modula.manager;

// 注意：移除对 modula.generated 的导入，避免循环依赖

export namespace concurrent_test {

/**
 * @brief 并发初始化策略
 * 
 * 使用modula.generated中的类型化并发初始化逻辑
 */
struct ConcurrentInitializationStrategy {
    /**
     * @brief 使用并发初始化策略初始化所有模块
     * @tparam Manager 管理器类型
     * @param manager 模块管理器实例
     * @return 如果所有模块初始化成功返回true
     */
    template<typename Manager>
    static bool initialize_all(Manager& manager) {
        std::cout << "\n=== 使用并发初始化策略 ===" << std::endl;
        
        // 使用标准初始化逻辑（并发功能已简化）
        return manager.initialize_all();
    }
    
    /**
     * @brief 使用并发关闭策略关闭所有模块
     * @tparam Manager 管理器类型
     * @param manager 模块管理器实例
     * @return 如果所有模块关闭成功返回true
     */
    template<typename Manager>
    static bool shutdown_all(Manager& manager) {
        std::cout << "\n=== 使用并发关闭策略 ===" << std::endl;
        
        // 使用标准关闭逻辑（并发功能已简化）
        return manager.shutdown_all();
    }
};

/**
 * @brief 并发模块管理器类型别名
 *
 * 使用标准的ModuleManager（策略模式已简化）
 */
using ConcurrentModuleManager = modula::ModuleManager;

} // namespace concurrent_test
