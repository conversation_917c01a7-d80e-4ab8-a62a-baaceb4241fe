#!/usr/bin/env python3
"""
@file __init__.py
@brief Modula Generator - Enhanced C++ Metadata Generation Tool

Modula Generator is a Python tool for generating C++ code from JSON metadata.
It can run as a standalone tool, independent of specific build systems or project structures.

Enhanced features:
- Generate C++ module registration code from JSON metadata files
- Advanced dependency analysis with cycle detection
- Parallel group support and initialization ordering
- Full metadata support compatible with metadata_generator
- Flexible template system with modern C++20/23 features
- Support both command line and API usage

@version 1.0.0
"""

__version__ = "1.0.0"
__author__ = "Modula Framework Team"
__description__ = "Enhanced Independent C++ Metadata Generation Tool"

# 公共接口
from .generator import ModulaGenerator, generate_from_json
from .config import Config
from .core import (
    ModulaGeneratorError,
    ConfigurationError,
    GenerationError,
    DependencyError
)

# 导出的主要类
__all__ = [
    'ModulaGenerator',
    'Config',
    'ModulaGeneratorError',
    'ConfigurationError',
    'GenerationError',
    'DependencyError',
    'generate_from_json',
    '__version__',
    '__author__',
    '__description__'
]