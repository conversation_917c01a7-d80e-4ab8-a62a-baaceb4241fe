/**
 * @file TestModule.module.ixx
 * @brief TestModule - 符合README.md规范的示例模块
 *
 * 这是一个完全符合README.md中模块定义规范的示例模块：
 * 条件1：通过enable_modula_framework()在CMake中声明
 * 条件2：主模块文件命名为<目录名>.module.ixx，使用export module语法
 * 条件3：实现符合modula.concepts concept Module的生命周期管理类
 * 条件4：通过modula.registry显式注册
 *
 * @version 3.0.0
 * <AUTHOR> Team
 */

module;

#include <iostream>
#include <string>
#include <chrono>

export module TestModule;

/**
 * @brief TestModule类 - 符合Module概念约束的生命周期管理类
 *
 * 这是主模块类，实现了README.md中条件3要求的概念约束：
 * - 符合modula.concepts concept Module的接口要求
 * - 实现完整的生命周期管理：initialize(), shutdown(), is_initialized()
 * - 支持[[modula::metadata]]属性标注系统
 * - 通过REGISTER_MODULE宏进行注册（条件4）
 *
 * 使用方式：
 * ```cpp
 * import TestModule;
 * import modula;
 *
 * auto manager = modula::initialize_framework();
 * auto* test_module = manager->get_module<TestModule>("TestModule");
 * ```
 */
export class TestModule {
public:
    /**
     * @brief 默认构造函数
     */
    TestModule() = default;

    /**
     * @brief 析构函数
     */
    ~TestModule() {
        if (initialized_) {
            shutdown();
        }
    }

    /**
     * @brief 初始化模块
     *
     * 实现Module概念要求的初始化接口
     * @return bool 初始化成功返回true，失败返回false
     */
    bool initialize() {
        if (initialized_) {
            std::cout << "[TestModule] Already initialized" << std::endl;
            return true;
        }

        std::cout << "[TestModule] Initializing..." << std::endl;

        // 模拟初始化过程
        initialization_time_ = std::chrono::steady_clock::now();

        // 这里可以添加实际的初始化逻辑
        // 例如：加载配置、初始化资源、建立连接等

        initialized_ = true;
        std::cout << "[TestModule] Initialization completed successfully" << std::endl;
        return true;
    }

    /**
     * @brief 关闭模块
     *
     * 实现Module概念要求的关闭接口
     */
    void shutdown() {
        if (!initialized_) {
            std::cout << "[TestModule] Already shutdown" << std::endl;
            return;
        }

        std::cout << "[TestModule] Shutting down..." << std::endl;

        // 这里可以添加实际的清理逻辑
        // 例如：释放资源、关闭连接、保存状态等

        initialized_ = false;
        std::cout << "[TestModule] Shutdown completed" << std::endl;
    }

    /**
     * @brief 检查模块是否已初始化
     *
     * 实现Module概念要求的状态查询接口
     * @return bool 已初始化返回true，否则返回false
     */
    bool is_initialized() const noexcept {
        return initialized_;
    }

    /**
     * @brief 获取模块版本（扩展接口）
     * @return std::string 版本号
     */
    std::string get_version() const {
        return "3.0.0";
    }

    /**
     * @brief 获取模块依赖列表（扩展接口）
     * @return std::vector<std::string> 依赖的模块名称列表
     */
    std::vector<std::string> get_dependencies() const {
        // TestModule没有依赖其他模块
        return {};
    }

    /**
     * @brief 检查是否支持热重载（扩展接口）
     * @return bool 是否支持热重载
     */
    bool supports_hot_reload() const noexcept {
        return false; // TestModule不支持热重载
    }

    /**
     * @brief 获取初始化时间
     * @return std::chrono::steady_clock::time_point 初始化时间点
     */
    auto get_initialization_time() const noexcept {
        return initialization_time_;
    }

    /**
     * @brief 获取运行时长
     * @return std::chrono::milliseconds 运行时长
     */
    auto get_uptime() const {
        if (!initialized_) {
            return std::chrono::milliseconds{0};
        }
        return std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - initialization_time_
        );
    }

private:
    bool initialized_ = false;  ///< 初始化状态标志
    std::chrono::steady_clock::time_point initialization_time_;  ///< 初始化时间戳
};
