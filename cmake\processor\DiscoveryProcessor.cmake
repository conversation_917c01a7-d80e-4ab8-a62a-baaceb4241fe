# ==============================================================================
# DiscoveryProcessor.cmake - Module Discovery Processor  
# ==============================================================================
#
# Discovers primary module files based on configurable patterns and extensions.
# Follows module definition rules and supports incremental caching.

cmake_minimum_required(VERSION 3.28)
include_guard(GLOBAL)

#[=======================================================================[.rst:
DiscoveryProcessor - Module Discovery Processor
===============================================

Provides module discovery functionality for the Modula Framework.

Core Responsibilities:
- Discover primary module files based on configurable patterns
- Follow module definition rules from project specifications
- Support file timestamp checking and incremental caching
- Set standardized target properties

Module Definition Rules:
- Module library should contain a primary module file (.ixx, .h, or .hpp format)
- Named as <module_name><_or.>module.<extension> or <module_name>.<extension>
- Module name based on target directory name (case insensitive)
- Primary module file must implement lifecycle management class conforming to modula.types concept

Main Interface:
- ``modula_process_discovery()`` - Process target list for module discovery

#]=======================================================================]

# ==============================================================================
# Dependencies
# ==============================================================================

include("${CMAKE_CURRENT_LIST_DIR}/../ModulaConfiguration.cmake")
include("${CMAKE_CURRENT_LIST_DIR}/../ModulaUtils.cmake")

# ==============================================================================
# Processor Registration
# ==============================================================================

if(COMMAND modula_register_processor)
    modula_register_processor("DiscoveryProcessor" "modula_process_discovery")
endif()

# ==============================================================================
# Discovery Configuration
# ==============================================================================

# Configuration loading state
set(_MODULA_DISCOVERY_CONFIG_LOADED FALSE CACHE INTERNAL "Discovery configuration loaded flag")

# ==============================================================================
# Main Processing Interface  
# ==============================================================================

#[=======================================================================[.rst:
.. command:: modula_process_discovery

  Process target list for module discovery.

  .. code-block:: cmake

    modula_process_discovery(<targets>)

  ``targets``
    List of targets to process

  Performs module discovery for specified targets following project specifications:
  - Pre-loads and caches configuration for batch processing
  - Cache checking to avoid duplicate processing
  - Searches for primary module files according to naming conventions
  - Sets target properties and caches results
  - Provides processing statistics and logging
#]=======================================================================]
function(modula_process_discovery targets)
    if(NOT targets)
        modula_message(VERBOSE "No targets provided for discovery" MODULE "DiscoveryProcessor")
        return()
    endif()

    modula_message(VERBOSE "Processing discovery for targets: ${targets}" MODULE "DiscoveryProcessor")

    # Pre-load configuration for batch processing
    _modula_discovery_ensure_config_loaded()

    set(processed_count 0)
    set(failed_count 0)

    foreach(target_name ${targets})
        # Target validity validation
        if(NOT TARGET ${target_name})
            modula_message(WARNING "Target '${target_name}' is invalid or does not exist" MODULE "DiscoveryProcessor")
            math(EXPR failed_count "${failed_count} + 1")
            continue()
        endif()

        # Cache check
        _modula_discovery_check_cache("${target_name}" cache_valid)
        if(cache_valid)
            modula_message(VERBOSE "Using cached discovery for '${target_name}'" MODULE "DiscoveryProcessor")
            math(EXPR processed_count "${processed_count} + 1")
            continue()
        endif()

        # Execute module discovery
        _modula_discovery_process_target("${target_name}" result)
        if(result)
            math(EXPR processed_count "${processed_count} + 1")
        else()
            math(EXPR failed_count "${failed_count} + 1")
        endif()
    endforeach()

    modula_message(VERBOSE "Discovery completed: ${processed_count} successful, ${failed_count} failed" MODULE "DiscoveryProcessor")
endfunction()

# ==============================================================================
# Configuration Management
# ==============================================================================

#[=======================================================================[.rst:
.. command:: _modula_discovery_ensure_config_loaded

  Ensure discovery configuration is loaded and cached.

  Pre-loads and caches discovery configuration to avoid repeated loading
  during batch processing. Retrieves configurable file patterns and extensions
  from ModulaConfiguration:

  - MODULA_MODULE_EXTENSIONS: Supported module file extensions (priority order)
  - MODULA_MODULE_NAMING_PATTERNS: Module file naming patterns (supports variable substitution)

  Naming Pattern Variables:
  - <module_name>: Replaced with module name (based on directory name)

  Performance:
  - Uses memory cache to avoid repeated configuration loading
  - Loads configuration once during batch processing
#]=======================================================================]
function(_modula_discovery_ensure_config_loaded)
    # Return early if already loaded
    if(_MODULA_DISCOVERY_CONFIG_LOADED)
        modula_cache_get("extensions" extensions_str NAMESPACE "discovery_config")
        modula_cache_get("patterns" patterns_str NAMESPACE "discovery_config")

        # Convert to list format
        string(REPLACE ";" "|" extensions_temp "${extensions_str}")
        string(REPLACE "|" ";" extensions "${extensions_temp}")

        string(REPLACE ";" "|" patterns_temp "${patterns_str}")
        string(REPLACE "|" ";" patterns "${patterns_temp}")

        set(_MODULA_DISCOVERY_MODULE_EXTENSIONS "${extensions}" PARENT_SCOPE)
        set(_MODULA_DISCOVERY_MODULE_NAMING_PATTERNS "${patterns}" PARENT_SCOPE)
        return()
    endif()

    # Get and cache extensions configuration (priority order)
    modula_get_config(MODULA_MODULE_EXTENSIONS extensions_str)
    if(NOT extensions_str)
        # Default extensions: C++20 module files, then traditional headers
        set(extensions_str ".ixx;.cppm;.mpp;.h;.hpp;.hxx")
    endif()
    modula_cache_set("extensions" "${extensions_str}" NAMESPACE "discovery_config")

    # Get and cache naming patterns configuration (priority order)
    modula_get_config(MODULA_MODULE_NAMING_PATTERNS patterns_str)
    if(NOT patterns_str)
        # Default naming patterns following project specifications:
        # 1. <module_name>_module.<extension> - highest priority
        # 2. <module_name>.module.<extension> - secondary priority
        # 3. <module_name>.<extension> - basic pattern
        set(patterns_str "<module_name>_module;<module_name>.module;<module_name>")
    endif()
    modula_cache_set("patterns" "${patterns_str}" NAMESPACE "discovery_config")

    # Convert to list format and set to parent scope
    string(REPLACE ";" "|" extensions_temp "${extensions_str}")
    string(REPLACE "|" ";" extensions "${extensions_temp}")

    string(REPLACE ";" "|" patterns_temp "${patterns_str}")
    string(REPLACE "|" ";" patterns "${patterns_temp}")

    set(_MODULA_DISCOVERY_MODULE_EXTENSIONS "${extensions}" PARENT_SCOPE)
    set(_MODULA_DISCOVERY_MODULE_NAMING_PATTERNS "${patterns}" PARENT_SCOPE)

    set(_MODULA_DISCOVERY_CONFIG_LOADED TRUE CACHE INTERNAL "Discovery configuration loaded flag" FORCE)
endfunction()

# ==============================================================================
# Core Discovery Logic
# ==============================================================================

#[=======================================================================[.rst:
.. command:: _modula_discovery_process_target

  Process a single target for module discovery.

  .. code-block:: cmake

    _modula_discovery_process_target(<target_name> <output_var>)

  ``target_name``
    Target name
  ``output_var``
    Variable to store processing result (TRUE/FALSE)

  Performs module discovery for specified target following project specifications:
  - Gets target source files and CXX_MODULE_SET_CXX_MODULES  
  - Calculates expected module name based on directory name
  - Uses configurable naming patterns and extensions to find primary module file
  - Sets standardized target properties and caches results

  Discovery Strategy:
  1. Merge all candidate files (CXX_MODULES + SOURCES)
  2. Search by naming pattern priority
  3. Search by extension priority
  4. Early exit optimization
#]=======================================================================]
function(_modula_discovery_process_target target_name output_var)
    # Get target information
    get_property(sources TARGET ${target_name} PROPERTY SOURCES)
    get_property(cxx_modules TARGET ${target_name} PROPERTY CXX_MODULE_SET_CXX_MODULES)
    get_property(source_dir TARGET ${target_name} PROPERTY SOURCE_DIR)

    # Expected module name (based on directory name)
    if(source_dir AND EXISTS "${source_dir}")
        get_filename_component(module_name "${source_dir}" NAME)
    else()
        # Fallback to target name
        set(module_name "${target_name}")
    endif()
    string(TOLOWER "${module_name}" module_name_normalized)

    # Merge all candidate files (treat CXX_MODULES and SOURCES equally)
    set(all_files ${cxx_modules} ${sources})

    # Resolve relative paths to absolute paths
    set(resolved_files "")
    foreach(file ${all_files})
        if(source_dir)
            modula_normalize_path("${file}" normalized_file BASE_DIR "${source_dir}")
        else()
            modula_normalize_path("${file}" normalized_file)
        endif()
        list(APPEND resolved_files "${normalized_file}")
    endforeach()

    # Find primary module file (by priority search)
    _modula_discovery_find_primary_file("${resolved_files}" "${module_name_normalized}" primary_file)

    # Process discovery results
    if(primary_file)
        # Successfully found primary module file
        modula_normalize_path("${primary_file}" primary_file)
        set_property(TARGET ${target_name} PROPERTY MODULA_MODULE_NAME "${module_name}")
        set_property(TARGET ${target_name} PROPERTY MODULA_MODULE_PRIMARY_FILE "${primary_file}")
        set_property(TARGET ${target_name} PROPERTY MODULA_MODULE_DISCOVERED TRUE)

        # Cache results
        set(cache_value "${primary_file}:${module_name}")
        modula_cache_set("${target_name}" "${cache_value}" NAMESPACE "discovery")

        set(${output_var} TRUE PARENT_SCOPE)
        modula_message(VERBOSE "Found module '${module_name}' in '${primary_file}'" MODULE "DiscoveryProcessor")
    else()
        # No primary module file found, set empty module properties
        set_property(TARGET ${target_name} PROPERTY MODULA_MODULE_NAME "")
        set_property(TARGET ${target_name} PROPERTY MODULA_MODULE_PRIMARY_FILE "")
        set_property(TARGET ${target_name} PROPERTY MODULA_MODULE_DISCOVERED TRUE)

        set(${output_var} FALSE PARENT_SCOPE)
        modula_message(VERBOSE "No primary module file found for '${target_name}'" MODULE "DiscoveryProcessor")
    endif()
endfunction()

# ==============================================================================
# Primary File Search
# ==============================================================================

#[=======================================================================[.rst:
.. command:: _modula_discovery_find_primary_file

  Find primary module file from file list.

  .. code-block:: cmake

    _modula_discovery_find_primary_file(<file_list> <module_name_normalized> <output_var>)

  ``file_list``
    List of files to search
  ``module_name_normalized``
    Normalized module name (lowercase)
  ``output_var``
    Variable to store found primary file path

  Searches for primary module file using configured patterns and extensions.
  Uses priority-based matching with early exit optimization.
#]=======================================================================]
function(_modula_discovery_find_primary_file file_list module_name_normalized output_var)
    set(primary_file "")

    # Resolve naming patterns
    set(resolved_patterns "")
    foreach(pattern ${_MODULA_DISCOVERY_MODULE_NAMING_PATTERNS})
        string(REPLACE "<module_name>" "${module_name_normalized}" resolved_pattern "${pattern}")
        string(TOLOWER "${resolved_pattern}" resolved_pattern)
        list(APPEND resolved_patterns "${resolved_pattern}")
    endforeach()

    # Search through files
    foreach(file ${file_list})
        # File existence check
        if(NOT EXISTS "${file}")
            continue()
        endif()

        # Extract file information
        get_filename_component(file_name "${file}" NAME_WE)
        get_filename_component(file_ext "${file}" EXT)
        string(TOLOWER "${file_name}" file_name_lower)

        # Check if extension is in supported extensions list
        set(ext_matched FALSE)
        foreach(supported_ext ${_MODULA_DISCOVERY_MODULE_EXTENSIONS})
            if(file_ext MATCHES "${supported_ext}$")
                set(ext_matched TRUE)
                break()
            endif()
        endforeach()

        if(NOT ext_matched)
            continue()
        endif()

        # Check all pre-resolved patterns by priority
        foreach(resolved_pattern ${resolved_patterns})
            if(file_name_lower STREQUAL "${resolved_pattern}")
                # Found exact match (pattern + extension)
                set(primary_file "${file}")
                break()  # Early exit: found matching file
            endif()
        endforeach()

        if(primary_file)
            break()  # Early exit: found matching file
        endif()
    endforeach()

    set(${output_var} "${primary_file}" PARENT_SCOPE)
endfunction()

# ==============================================================================
# Cache System
# ==============================================================================

#[=======================================================================[.rst:
.. command:: _modula_discovery_check_cache

  Check target's discovery cache.

  .. code-block:: cmake

    _modula_discovery_check_cache(<target_name> <output_var>)

  ``target_name``
    Target name
  ``output_var``
    Variable to store cache check result (TRUE/FALSE)

  Cache checking:
  - Uses memory cache for discovery results
  - Checks primary module file existence
  - Automatically applies valid cached results
  - Provides detailed invalidation reasons when cache fails

  Cache Format: "<primary_file>:<module_name>"

  Cache Invalidation Conditions:
  - Primary module file does not exist
  - Cache format is invalid
#]=======================================================================]
function(_modula_discovery_check_cache target_name output_var)
    modula_cache_get("${target_name}" cached_result NAMESPACE "discovery")
    if(NOT cached_result)
        set(${output_var} FALSE PARENT_SCOPE)
        return()
    endif()

    # Parse cache result format: <primary_file>:<module_name>
    string(REPLACE ":" ";" result_parts "${cached_result}")
    list(LENGTH result_parts parts_count)

    if(parts_count LESS 2)
        modula_message(VERBOSE "Cache invalidated for '${target_name}': invalid cache format" MODULE "DiscoveryProcessor")
        set(${output_var} FALSE PARENT_SCOPE)
        return()
    endif()

    list(GET result_parts 0 cached_file)

    # Check if primary module file exists
    if(NOT EXISTS "${cached_file}")
        modula_message(VERBOSE "Cache invalidated for '${target_name}': file '${cached_file}' not found" MODULE "DiscoveryProcessor")
        set(${output_var} FALSE PARENT_SCOPE)
        return()
    endif()

    # Cache is valid, apply cached results
    _modula_discovery_apply_cached_result("${target_name}" "${cached_result}")
    set(${output_var} TRUE PARENT_SCOPE)
endfunction()

#[=======================================================================[.rst:
.. command:: _modula_discovery_apply_cached_result

  Apply cached discovery results.

  .. code-block:: cmake

    _modula_discovery_apply_cached_result(<target_name> <cached_result>)

  ``target_name``
    Target name
  ``cached_result``
    Cached discovery result string

  Applies cached discovery results to target properties:
  - Parses cache result format: <primary_file>:<module_name>  
  - Sets module-related properties

  Target Properties Set:
  - MODULA_MODULE_NAME: Module name
  - MODULA_MODULE_PRIMARY_FILE: Primary module file path
  - MODULA_MODULE_DISCOVERED: Discovery status flag
#]=======================================================================]
function(_modula_discovery_apply_cached_result target_name cached_result)
    string(REPLACE ":" ";" result_parts "${cached_result}")
    list(GET result_parts 0 primary_file)
    list(GET result_parts 1 module_name)

    # Set target properties
    set_property(TARGET ${target_name} PROPERTY MODULA_MODULE_NAME "${module_name}")
    set_property(TARGET ${target_name} PROPERTY MODULA_MODULE_PRIMARY_FILE "${primary_file}")
    set_property(TARGET ${target_name} PROPERTY MODULA_MODULE_DISCOVERED TRUE)
endfunction()

message(STATUS "Modula: Module discovery processor loaded")
