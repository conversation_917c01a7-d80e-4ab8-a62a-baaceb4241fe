#!/usr/bin/env python3
"""
数据处理和代码生成层 - 简化的元数据处理管道

实现所有具体的处理器类/函数：
- JSON数据源读取器
- C++代码生成器
- 文件输出处理器
- 数据转换和验证逻辑

各个处理器分工明确，具有明确的输入和输出，避免冗余的函数设计。
"""

import json
import os
import logging
import time
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from datetime import datetime

from configuration import (
    Config, TemplateRenderer, PathManager,
    get_common_template_variables
)
from templates import (
    TemplateManager, TemplateContext, create_template_context,
    create_template_manager, render_module_template, render_global_template
)


@dataclass
class ModuleData:
    """标准化的模块数据结构 - 与CMake MetadataProcessor兼容"""
    name: str
    target_name: str
    version: str = "1.0.0"
    directory: str = ""
    interface_file: str = ""
    dependencies: List[str] = field(default_factory=list)
    sources: List[str] = field(default_factory=list)
    build_config: Dict[str, Any] = field(default_factory=dict)
    output_config: Dict[str, Any] = field(default_factory=dict)
    link_info: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    generated_at: str = field(default_factory=lambda: datetime.now().isoformat())

    # 并行初始化支持
    parallel_group: int = 0      # 并行组编号 (0 = 顺序初始化)
    priority: int = 0            # 组内优先级 (数值越高优先级越高)

    # 路径信息（用于正确计算相对路径）
    source_dir: str = ""         # 项目源目录
    binary_dir: str = ""         # 项目构建目录

    def validate(self) -> bool:
        """验证模块数据的完整性"""
        return bool(self.name and self.target_name)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式 - 与CMake MetadataProcessor兼容"""
        return {
            'name': self.name,
            'version': self.version,
            'directory': self.directory,
            'interface': self.interface_file,
            'dependencies': self.dependencies,
            'sources': self.sources,
            'build_config': self.build_config,
            'output_config': self.output_config,
            'link_info': self.link_info,
            'metadata': self.metadata,
            'generated_at': self.generated_at
        }


@dataclass
class DependencyAnalysisResult:
    """依赖分析结果"""
    initialization_order: List[str] = field(default_factory=list)
    parallel_groups: List[List[str]] = field(default_factory=list)
    dependency_matrix: Dict[str, List[str]] = field(default_factory=dict)
    type_mappings: Dict[str, str] = field(default_factory=dict)
    has_cycles: bool = False
    cycles: List[List[str]] = field(default_factory=list)
    max_depth: int = 0


@dataclass
class ProcessingStatistics:
    """处理统计信息"""
    modules_processed: int = 0
    files_generated: int = 0
    processing_time: float = 0.0
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    dependency_analysis: Optional[DependencyAnalysisResult] = None


class JsonDataReader:
    """JSON数据源读取器"""

    def __init__(self, json_file: str):
        self.json_file = json_file
        self.logger = logging.getLogger("json_reader")

    def read_modules(self) -> List[ModuleData]:
        """读取模块数据"""
        try:
            with open(self.json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            if 'modules' not in data:
                self.logger.warning("No 'modules' key found in JSON file")
                return []

            # 从全局元数据中获取路径信息
            metadata = data.get('metadata', {})
            source_dir = metadata.get('source_dir', '')
            binary_dir = metadata.get('binary_dir', '')

            modules = []
            for module_data in data['modules']:
                module = ModuleData(
                    name=module_data.get('name', ''),
                    target_name=module_data.get('target_name', module_data.get('build_config', {}).get('target_name', '')),
                    version=module_data.get('version', '1.0.0'),
                    directory=module_data.get('directory', ''),
                    interface_file=module_data.get('interface_file', module_data.get('interface', '')),
                    dependencies=module_data.get('dependencies', []),
                    sources=module_data.get('sources', []),
                    build_config=module_data.get('build_config', {}),
                    output_config=module_data.get('output_config', {}),
                    link_info=module_data.get('link_info', {}),
                    metadata=module_data.get('metadata', {}),
                    generated_at=module_data.get('generated_at', ''),
                    source_dir=source_dir,
                    binary_dir=binary_dir
                )

                if module.validate():
                    modules.append(module)
                else:
                    self.logger.warning(f"Invalid module data: {module_data}")

            self.logger.info(f"Successfully read {len(modules)} modules from {self.json_file}")
            return modules

        except Exception as e:
            self.logger.error(f"Error reading JSON file {self.json_file}: {e}")
            return []


class DependencyAnalyzer:
    """构建时依赖分析器 - 实现依赖图算法"""

    def __init__(self, modules: List[ModuleData]):
        self.modules = modules
        self.logger = logging.getLogger("dependency_analyzer")
        self.dependency_graph: Dict[str, List[str]] = {}
        self.reverse_graph: Dict[str, List[str]] = {}

    def analyze(self) -> DependencyAnalysisResult:
        """执行完整的依赖分析"""
        self.logger.info("Starting dependency analysis...")

        # 1. 构建依赖图
        self._build_dependency_graph()

        # 2. 检测循环依赖
        cycles = self._detect_cycles()

        if cycles:
            self.logger.error(f"Circular dependencies detected: {cycles}")
            return DependencyAnalysisResult(
                has_cycles=True,
                cycles=cycles,
                dependency_matrix=self.dependency_graph
            )

        # 3. 拓扑排序
        topo_order = self._topological_sort()

        # 4. 识别并行组
        parallel_groups = self._identify_parallel_groups(topo_order)

        # 5. 构建类型映射
        type_mappings = self._build_type_mappings()

        # 6. 计算最大深度
        max_depth = self._calculate_max_depth()

        result = DependencyAnalysisResult(
            initialization_order=topo_order,
            parallel_groups=parallel_groups,
            dependency_matrix=self.dependency_graph,
            type_mappings=type_mappings,
            has_cycles=False,
            cycles=[],
            max_depth=max_depth
        )

        self.logger.info(f"Dependency analysis completed. Order: {topo_order}")
        return result

    def _build_dependency_graph(self):
        """构建依赖图"""
        # 初始化图
        for module in self.modules:
            self.dependency_graph[module.name] = module.dependencies.copy()
            self.reverse_graph[module.name] = []

        # 构建反向图
        for module_name, deps in self.dependency_graph.items():
            for dep in deps:
                if dep in self.reverse_graph:
                    self.reverse_graph[dep].append(module_name)

    def _detect_cycles(self) -> List[List[str]]:
        """检测循环依赖 - 使用DFS算法"""
        visited = set()
        rec_stack = set()
        cycles = []

        def dfs(node: str, path: List[str]) -> bool:
            if node in rec_stack:
                # 找到循环
                cycle_start = path.index(node)
                cycle = path[cycle_start:] + [node]
                cycles.append(cycle)
                return True

            if node in visited:
                return False

            visited.add(node)
            rec_stack.add(node)
            path.append(node)

            for neighbor in self.dependency_graph.get(node, []):
                if neighbor in self.dependency_graph:  # 确保邻居存在
                    if dfs(neighbor, path):
                        return True

            rec_stack.remove(node)
            path.pop()
            return False

        for module_name in self.dependency_graph:
            if module_name not in visited:
                dfs(module_name, [])

        return cycles

    def _topological_sort(self) -> List[str]:
        """拓扑排序 - Kahn算法"""
        # 计算入度 - 计算每个模块依赖的模块数量作为入度
        in_degree = {}
        for module_name, deps in self.dependency_graph.items():
            in_degree[module_name] = len(deps)

        # 初始化队列 - 将入度为0的模块（无依赖的模块）加入队列
        queue = [name for name, degree in in_degree.items() if degree == 0]
        result = []

        while queue:
            # 按字母顺序排序以确保确定性结果
            queue.sort()
            node = queue.pop(0)
            result.append(node)

            # 更新依赖该节点的模块的入度
            for neighbor in self.reverse_graph.get(node, []):
                in_degree[neighbor] -= 1
                if in_degree[neighbor] == 0:
                    queue.append(neighbor)

        return result

    def _identify_parallel_groups(self, topo_order: List[str]) -> List[List[str]]:
        """识别可并行初始化的模块组"""
        if not topo_order:
            return []

        # 计算每个模块的层级
        levels = {}
        for module in topo_order:
            max_dep_level = -1
            for dep in self.dependency_graph.get(module, []):
                if dep in levels:
                    max_dep_level = max(max_dep_level, levels[dep])
            levels[module] = max_dep_level + 1

        # 按层级分组
        level_groups = {}
        for module, level in levels.items():
            if level not in level_groups:
                level_groups[level] = []
            level_groups[level].append(module)

        # 转换为列表格式
        parallel_groups = []
        for level in sorted(level_groups.keys()):
            parallel_groups.append(sorted(level_groups[level]))

        return parallel_groups

    def _build_type_mappings(self) -> Dict[str, str]:
        """构建类型映射"""
        type_mappings = {}
        for module in self.modules:
            # 模块名即为类型名（按照设计文档）
            type_mappings[module.name] = module.name
        return type_mappings

    def _calculate_max_depth(self) -> int:
        """计算依赖图的最大深度"""
        def get_depth(module: str, visited: set) -> int:
            if module in visited:
                return 0  # 避免循环

            visited.add(module)
            max_dep_depth = 0

            for dep in self.dependency_graph.get(module, []):
                if dep in self.dependency_graph:
                    dep_depth = get_depth(dep, visited.copy())
                    max_dep_depth = max(max_dep_depth, dep_depth)

            return max_dep_depth + 1

        max_depth = 0
        for module_name in self.dependency_graph:
            depth = get_depth(module_name, set())
            max_depth = max(max_depth, depth)

        return max_depth


class CppCodeGenerator:
    """C++代码生成器"""

    def __init__(self, output_directory: str):
        self.output_directory = output_directory
        self.logger = logging.getLogger("cpp_generator")
        self.generated_files = []

        self.template_manager = create_template_manager()
        self.legacy_renderer = TemplateRenderer()  # 保留用于向后兼容

    def generate_all(self, modules: List[ModuleData], dependency_result: Optional[DependencyAnalysisResult] = None) -> bool:
        """生成所有C++文件"""
        try:
            # 确保输出目录存在
            PathManager.ensure_output_directory(self.output_directory)

            # 生成全局文件
            if not self._generate_registry_file(modules, dependency_result):
                return False

            if not self.generate_companion_files(modules, dependency_result):
                return False

            self.logger.info(f"Generated {len(self.generated_files)} C++ files (refactored architecture)")
            return True

        except Exception as e:
            self.logger.error(f"C++ code generation failed: {e}")
            return False

    def generate_companion_files(self, modules: List[ModuleData], dependency_result: Optional[DependencyAnalysisResult] = None) -> bool:
        """生成伴生.gen.cpp文件（新架构模式）"""
        # 1. 分析并分配并行组
        parallel_analyzer = ParallelGroupAnalyzer(modules)
        parallel_analyzer.analyze_parallel_groups()

        # 2. 记录并行组分析结果
        init_plan = parallel_analyzer.get_initialization_plan()
        self.logger.info(f"Parallel group analysis completed: {init_plan['total_groups']} groups")
        for group in init_plan['groups']:
            module_names = [m['name'] for m in group['modules']]
            self.logger.info(f"  Group {group['group_id']}: {', '.join(module_names)}")

        # 3. 为每个模块生成伴生文件
        for module in modules:
            if not self._generate_companion_file(module):
                return False

        self.logger.info(f"Generated {len(self.generated_files)} companion files")
        return True

    def _generate_companion_file(self, module: ModuleData) -> bool:
        """生成C++伴生文件 (.gen.cpp)"""
        try:
            output_file = PathManager.get_companion_file_path(self.output_directory, module.name)

            # 准备模板上下文
            context = create_template_context()
            context.update(**self._get_companion_variables(module))

            # 使用伴生文件模板
            content = self.template_manager.render_template('module', context)

            # 写入文件
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(content)

            self.generated_files.append(output_file)
            self.logger.debug(f"Successfully generated companion file: {output_file}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to generate companion file for {module.name}: {e}")
            return False

    def _get_companion_variables(self, module: ModuleData) -> dict:
        """获取C++伴生文件的模板变量"""
        from templates import TemplateRenderer as TemplateRenderer

        # 生成依赖数组
        dependency_names_array = self._generate_dependency_names_array(module)
        source_files_array = self._generate_source_files_array(module)

        # 智能选择模块导入方式
        module_import_statement = self._generate_module_import_statement(module)

        # 生成元数据定义
        metadata_definition = self._generate_metadata_definition(module, dependency_names_array, source_files_array)

        return {
            'module_name': module.name,
            'module_target_name': module.target_name,
            'module_class_name': module.name,
            'version': module.version,
            'directory': TemplateRenderer.escape_cpp_string(module.directory),
            'interface_file': TemplateRenderer.escape_cpp_string(module.interface_file),
            'dependency_count': len(module.dependencies),
            'dependency_names_array': dependency_names_array,
            'source_count': len(module.sources),
            'source_files_array': source_files_array,
            'parallel_group': module.parallel_group,
            'priority': module.priority,
            'generation_time': datetime.now().isoformat(),
            'module_import_statement': self._generate_module_import_statement(module),
        }

    def _generate_module_import_statement(self, module: ModuleData) -> str:
        """生成模块导入语句"""
        # - 对于使用C++20 modules的模块（.ixx文件），使用import语句
        # - 对于传统头文件模块（.h文件），使用相对路径导入
        if module.interface_file.endswith('.ixx') or module.interface_file.endswith('.cppm') or module.interface_file.endswith('.mpp'):
            # C++20 modules：使用import语句
            return f'import {module.name};'
        else:
            # 传统头文件：使用相对路径导入
            import os

            # 获取接口文件的绝对路径
            interface_file_path = module.interface_file

            # 如果接口文件路径不是绝对路径，需要根据情况处理
            if not os.path.isabs(interface_file_path):
                # 首先尝试从模块目录构建绝对路径
                if module.directory and os.path.isabs(module.directory):
                    interface_file_path = os.path.join(module.directory, interface_file_path)
                elif module.source_dir:
                    # 如果模块目录不是绝对路径，使用源目录作为基础
                    interface_file_path = os.path.join(module.source_dir, interface_file_path)
                else:
                    # 最后的回退选项：使用当前工作目录
                    interface_file_path = os.path.abspath(interface_file_path)

            # 获取生成文件的目录（.gen.cpp文件所在目录）
            generated_file_dir = self.output_directory
            if not os.path.isabs(generated_file_dir) and module.binary_dir:
                # 如果输出目录不是绝对路径，使用构建目录作为基础
                generated_file_dir = os.path.join(module.binary_dir, generated_file_dir)

            # 计算从生成文件目录到接口文件的相对路径
            try:
                relative_path = os.path.relpath(interface_file_path, generated_file_dir)
                # 标准化路径分隔符为正斜杠（C++标准）
                relative_path = relative_path.replace('\\', '/')
                return f'#include "{relative_path}"'
            except (ValueError, OSError) as e:
                # 如果相对路径计算失败，回退到绝对路径
                self.logger.warning(f"Failed to calculate relative path for {module.name}: {e}, using absolute path")
                absolute_path = interface_file_path.replace('\\', '/')
                return f'#include "{absolute_path}"'

    def _generate_metadata_definition(self, module: ModuleData, dependency_names_array: str, source_files_array: str) -> str:
        """生成元数据定义代码"""
        from templates import TemplateRenderer as TemplateRenderer
        return f"""return Metadata{{
        "{module.name}",
        "{module.name}",
        "{TemplateRenderer.escape_cpp_string(module.version)}",
        "{TemplateRenderer.escape_cpp_string(module.directory)}",
        "{TemplateRenderer.escape_cpp_string(module.interface_file)}",
        std::span<const std::string_view>{{detail::module_dependencies}},
        std::span<const std::string_view>{{detail::module_source_files}},
        __TIMESTAMP__,
        {getattr(module, 'parallel_group', 0)},
        {getattr(module, 'priority', 0)}
    }};"""


    def _generate_registry_file(self, modules: List[ModuleData], dependency_result: Optional[DependencyAnalysisResult] = None) -> bool:
        """生成全局文件"""
        try:
            output_file = PathManager.get_generated_path(self.output_directory)

            # 准备模板上下文
            context = create_template_context()

            # 基础变量
            context.update(
                total_modules=len(modules),
                module_imports=self._generate_module_imports(modules),
                module_names_array=self._generate_module_names_array(modules)
            )

            # 根据是否有依赖分析结果，生成条件性内容
            if dependency_result and not dependency_result.has_cycles:
                # 检查是否需要并发初始化器
                has_parallel_groups = (len(dependency_result.parallel_groups) > 1 and
                                     any(len(group) > 1 for group in dependency_result.parallel_groups))

                # 计算最大依赖深度
                max_dependency_depth = dependency_result.max_depth if dependency_result.max_depth > 0 else 1

                # 有依赖分析结果，生成完整的类型化功能
                context.update(
                    conditional_module_forward_declarations=self._generate_module_forward_declarations(modules),
                    conditional_typed_features=self._generate_simplified_typed_features(modules, dependency_result),
                    max_dependency_depth=max_dependency_depth,
                    has_parallel_groups='true' if has_parallel_groups else 'false'
                )
            else:
                # 无依赖分析结果，生成基础功能
                context.update(
                    conditional_module_forward_declarations=self._generate_module_forward_declarations(modules),
                    conditional_typed_features=self._generate_basic_typed_features(modules),
                    max_dependency_depth=1,
                    has_parallel_groups='false'
                )

            # 使用全局模板
            content = self.template_manager.render_template('global', context)

            # 写入文件
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(content)

            self.generated_files.append(output_file)
            return True

        except Exception as e:
            self.logger.error(f"Failed to generate global file: {e}")
            return False

    def _generate_dependency_names_array(self, module: ModuleData) -> str:
        """生成依赖名称数组内容"""
        if not module.dependencies:
            return ""

        escaped_deps = [f'        "{TemplateRenderer.escape_cpp_string(dep)}"' for dep in module.dependencies]
        return ',\n'.join(escaped_deps)

    def _generate_source_files_array(self, module: ModuleData) -> str:
        """生成源文件数组内容"""
        if not module.sources:
            return ""

        escaped_sources = [f'        "{TemplateRenderer.escape_cpp_string(src)}"' for src in module.sources]
        return ',\n'.join(escaped_sources)

    def _generate_simplified_typed_features(self, modules: List[ModuleData], dependency_result: DependencyAnalysisResult) -> str:
        """生成简化的类型化功能部分（现代C++20/23方式）"""
        return f'''
// ============================================================================
// 函数模板声明（用于特化）
// ============================================================================

/**
 * @brief 获取模块元数据的函数模板
 * @tparam T 模块类型
 * @return 模块的元数据信息
 */
template<typename T>
constexpr modula::Metadata get_module_metadata();

/**
 * @brief 获取模块类型标签的函数模板
 * @tparam T 模块类型
 * @return 模块的类型标签
 */
template<typename T>
constexpr std::size_t meta_type_tag();

/**
 * @brief 获取模块类型索引的函数模板
 * @tparam T 模块类型
 * @return 模块的类型索引
 */
template<typename T>
constexpr std::size_t meta_type_index();

/**
 * @brief 检查模块是否已注册的函数模板
 * @tparam T 模块类型
 * @return 是否已注册
 */
template<typename T>
constexpr bool is_type_registered();

/**
 * @brief 类型化的初始化顺序
 */
using initialization_order = {self._generate_typed_initialization_order(dependency_result)};

/**
 * @brief 类型化的依赖关系定义
 */
template<typename T>
struct module_dependencies {{
    using dependencies = type_list<>;  // 默认无依赖
}};

{self._generate_typed_dependencies(modules, dependency_result)}

{self._generate_parallel_groups_code(dependency_result)}'''

    def _generate_basic_typed_features(self, modules: List[ModuleData]) -> str:
        """生成基础的类型化功能（无依赖分析时）"""
        return '''
/**
 * @brief 基础模式：无类型化依赖分析功能
 *
 * 依赖分析结果不可用或存在循环依赖。
 * 仅提供基本的模块信息。
 */
template<typename T>
struct module_dependencies {
    using dependencies = type_list<>;  // 默认无依赖
};'''

    def _generate_typed_features_section(self, modules: List[ModuleData], dependency_result: DependencyAnalysisResult) -> str:
        """生成类型化功能部分 - 简化版本（现代C++20/23方式）"""
        return self._generate_simplified_typed_features(modules, dependency_result)

    def _generate_dependencies_section(self, module: ModuleData) -> str:
        """生成依赖关系部分"""
        if not module.dependencies:
            return '''/**
 * @brief No dependencies for this module
 */
inline constexpr std::array<std::string_view, 0> dependencies = {};
inline constexpr std::size_t dependency_count = 0;'''

        dep_count = len(module.dependencies)
        deps_array = ',\n    '.join(f'"{TemplateRenderer.escape_cpp_string(dep)}"' for dep in module.dependencies)

        return f'''/**
 * @brief Compile-time dependency list
 */
inline constexpr std::array<std::string_view, {dep_count}> dependencies = {{
    {deps_array}
}};
inline constexpr std::size_t dependency_count = {dep_count};'''

    def _generate_sources_section(self, module: ModuleData) -> str:
        """生成源文件部分"""
        if not module.sources:
            return '''/**
 * @brief No source files recorded for this module
 */
inline constexpr std::array<std::string_view, 0> source_files = {};
inline constexpr std::size_t source_count = 0;'''

        source_count = len(module.sources)
        sources_array = ',\n    '.join(f'"{TemplateRenderer.escape_cpp_string(src)}"' for src in module.sources)

        return f'''/**
 * @brief Source files in this module
 */
inline constexpr std::array<std::string_view, {source_count}> source_files = {{
    {sources_array}
}};
inline constexpr std::size_t source_count = {source_count};'''

    def _generate_dependency_types(self, module: ModuleData) -> str:
        """生成依赖类型列表"""
        if not module.dependencies:
            return "type_list<>"

        type_list = ', '.join(module.dependencies)
        return f"type_list<{type_list}>"

    def _generate_module_imports(self, modules: List[ModuleData]) -> str:
        """生成模块导入语句"""
        # 暂时不生成元数据模块导入，因为这些模块不存在
        # 在未来版本中，如果需要可以重新启用
        return "// 模块元数据导入已禁用，避免编译错误"

    def _generate_module_names_array(self, modules: List[ModuleData]) -> str:
        """生成模块名称数组内容"""
        if not modules:
            return ""

        escaped_names = [f'    "{TemplateRenderer.escape_cpp_string(module.name)}"' for module in modules]
        return ',\n'.join(escaped_names)

    def _generate_module_reexports(self, modules: List[ModuleData]) -> str:
        """生成模块重新导出语句"""
        if not modules:
            return ""

        reexports = []
        for module in modules:
            reexports.append(f'export import {module.name}.metadata;')

        return '\n'.join(reexports)

    def _generate_module_forward_declarations(self, modules: List[ModuleData]) -> str:
        """生成模块前向声明"""
        if not modules:
            return ""

        declarations = []
        for module in modules:
            declarations.append(f"class {module.name};")

        return '\n'.join(declarations)

    def _generate_typed_initialization_order(self, dependency_result: DependencyAnalysisResult) -> str:
        """生成类型化的初始化顺序"""
        if not dependency_result.initialization_order:
            return "type_list<>"

        types = ', '.join(dependency_result.initialization_order)
        return f"type_list<{types}>"

    def _generate_typed_dependencies(self, modules: List[ModuleData], dependency_result: DependencyAnalysisResult) -> str:
        """生成类型化的依赖关系定义"""
        if not modules:
            return ""

        dependencies_code = []

        for module in modules:
            if module.dependencies:
                deps = ', '.join(module.dependencies)
                dependencies_code.append(f"""template<>
struct module_dependencies<{module.name}> {{
    using dependencies = type_list<{deps}>;
}};""")

        return '\n\n'.join(dependencies_code)

    def _generate_parallel_groups_code(self, dependency_result: DependencyAnalysisResult) -> str:
        """生成并行组的C++代码定义"""
        if not dependency_result.parallel_groups:
            return ""

        code_lines = []
        code_lines.append("/**")
        code_lines.append(" * @brief 编译时并行组定义")
        code_lines.append(" */")
        code_lines.append("")

        # 生成每个并行组的类型列表
        for i, group in enumerate(dependency_result.parallel_groups):
            if len(group) == 1:
                # 单模块组
                code_lines.append(f"using level_{i}_group = type_list<{group[0]}>;")
            else:
                # 多模块并行组
                type_list = ", ".join(group)
                code_lines.append(f"using level_{i}_group = type_list<{type_list}>;")

        code_lines.append("")

        # 生成完整的并行组序列
        group_types = [f"level_{i}_group" for i in range(len(dependency_result.parallel_groups))]
        code_lines.append(f"using parallel_execution_plan = type_list<{', '.join(group_types)}>;")

        return '\n'.join(code_lines)

    def _generate_concurrent_initialization_functions(self, dependency_result: DependencyAnalysisResult) -> str:
        """生成并发初始化函数"""
        if not dependency_result.parallel_groups or len(dependency_result.parallel_groups) <= 1:
            return ""

        code_lines = []
        # code_lines.append("")
        # code_lines.append("/**")
        # code_lines.append(" * @brief 并发初始化函数")
        # code_lines.append(" */")
        # code_lines.append("template<typename ManagerType>")
        # code_lines.append("constexpr bool initialize_all_concurrent(ManagerType& manager) {")
        # code_lines.append("    using plan = parallel_execution_plan;")
        # code_lines.append("    return concurrent_initializer<plan>::execute(manager);")
        # code_lines.append("}")
        # code_lines.append("")
        code_lines.append("/**")
        code_lines.append(" * @brief 顺序初始化函数")
        code_lines.append(" */")
        code_lines.append("template<typename ManagerType>")
        code_lines.append("constexpr bool initialize_all_sequential(ManagerType& manager) {")
        code_lines.append("    using order = initialization_order;")
        code_lines.append("    return sequence_initializer<order>::initialize(manager);")
        code_lines.append("}")
        # code_lines.append("")
        # code_lines.append("/**")
        # code_lines.append(" * @brief 并发关闭函数")
        # code_lines.append(" */")
        # code_lines.append("template<typename ManagerType>")
        # code_lines.append("constexpr bool shutdown_all_concurrent(ManagerType& manager) {")
        # code_lines.append("    using plan = parallel_execution_plan;")
        # code_lines.append("    return concurrent_initializer<plan>::shutdown(manager);")
        # code_lines.append("}")

        return '\n'.join(code_lines)

    def _generate_typed_initialize_function(self, dependency_result: DependencyAnalysisResult) -> str:
        """生成类型化的初始化函数 - 根据模块数量和并行组优化策略"""
        if not dependency_result.initialization_order:
            return "return true;"

        module_count = len(dependency_result.initialization_order)
        has_parallel_groups = len(dependency_result.parallel_groups) > 1 and any(len(group) > 1 for group in dependency_result.parallel_groups)

        self.logger.info(f"Generating initialization function for {module_count} modules, parallel groups: {has_parallel_groups}")

        # 如果有并行组且配置支持并发，生成并发初始化代码
        if has_parallel_groups and module_count > 2:
            self.logger.debug("Using concurrent initialization strategy with parallel groups")
            return """// 智能选择初始化策略
    if (manager.get_config().enable_concurrent_initialization) {
        return initialize_all_concurrent(manager);
    } else {
        return initialize_all_sequential(manager);
    }"""

        elif module_count <= 5:
            # 策略1: 完全展开 - 最佳性能，适用于少量模块
            self.logger.debug("Using unrolled strategy for optimal performance")
            init_calls = []
            for module in dependency_result.initialization_order:
                init_calls.append(f"manager.template initialize_module<{module}>()")
            join_str = ' &&\n           '
            return f"return {join_str.join(init_calls)};"

        elif module_count <= 20:
            # 策略2: 折叠表达式 - 平衡性能和编译时间
            self.logger.debug("Using fold expression strategy for balanced performance")
            return """using order = generated::initialization_order;
    return sequence_initializer<order>::initialize(manager);"""

        else:
            # 策略3: 模板递归 - 适用于大量模块，减少编译时间
            self.logger.debug("Using template recursion strategy for large module count")
            return """using order = generated::initialization_order;
    return sequence_initializer<order>::initialize(manager);"""

    def _generate_typed_shutdown_function(self, dependency_result: DependencyAnalysisResult) -> str:
        """生成类型化的关闭函数"""
        if not dependency_result.initialization_order:
            return "return true;"

        # 逆序关闭
        return """using order = generated::initialization_order;
    return sequence_initializer<order>::shutdown(manager);"""


class JsonOutputGenerator:
    """JSON输出生成器"""

    def __init__(self, output_directory: str):
        self.output_directory = output_directory
        self.logger = logging.getLogger("json_generator")

    def generate(self, modules: List[ModuleData]) -> bool:
        """生成JSON元数据文件 - 与CMake MetadataProcessor兼容"""
        try:
            output_file = PathManager.get_json_metadata_path(self.output_directory)

            # 构建JSON数据 - 与CMake格式兼容
            data = {
                'metadata': {
                    'generator': 'modula_metadata_generator',
                    'generated_at': datetime.now().isoformat(),
                    'project': 'Modula',
                    'total_modules': len(modules)
                },
                'modules': [module.to_dict() for module in modules]
            }

            # 写入文件
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            self.logger.info(f"Generated JSON metadata file: {output_file}")
            return True

        except Exception as e:
            self.logger.error(f"Error generating JSON metadata: {e}")
            return False


class MetadataProcessor:
    """主元数据处理器 - 协调所有处理步骤"""

    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger("processor")
        self.statistics = ProcessingStatistics()
        self.start_time = 0.0

    def process(self) -> bool:
        """执行完整的处理流程"""
        self.start_time = time.time()

        try:
            # 1. 读取JSON数据
            self.logger.info("Reading JSON data...")
            reader = JsonDataReader(self.config.json_file)
            modules = reader.read_modules()

            if not modules:
                self.logger.error("No valid modules found")
                return False

            self.statistics.modules_processed = len(modules)
            self.logger.info(f"Loaded {len(modules)} modules")

            # 2. 执行依赖分析
            self.logger.info("Performing dependency analysis...")
            analyzer = DependencyAnalyzer(modules)
            dependency_result = analyzer.analyze()
            self.statistics.dependency_analysis = dependency_result

            if dependency_result.has_cycles:
                self.logger.error("Cannot proceed due to circular dependencies")
                return False

            self.logger.info(f"Dependency analysis successful. Initialization order: {dependency_result.initialization_order}")

            # 3. 生成C++代码（如果启用）
            if self.config.enable_cpp_generation:
                self.logger.info("Generating C++ metadata files...")
                cpp_generator = CppCodeGenerator(self.config.output_directory)
                # 传递依赖分析结果给代码生成器
                if not cpp_generator.generate_all(modules, dependency_result):
                    self.logger.error("C++ code generation failed")
                    return False
                self.statistics.files_generated += len(cpp_generator.generated_files)

            # 4. 生成JSON输出（如果启用）
            if self.config.enable_json_generation:
                self.logger.info("Generating JSON metadata file...")
                json_generator = JsonOutputGenerator(self.config.output_directory)
                if not json_generator.generate(modules):
                    self.logger.error("JSON generation failed")
                    return False
                self.statistics.files_generated += 1

            # 5. 计算处理时间
            self.statistics.processing_time = time.time() - self.start_time

            self.logger.info("Metadata processing completed successfully")
            return True

        except Exception as e:
            self.logger.error(f"Processing failed: {e}")
            self.statistics.errors.append(str(e))
            return False

    def get_statistics(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        return {
            'modules_processed': self.statistics.modules_processed,
            'files_generated': self.statistics.files_generated,
            'processing_time': self.statistics.processing_time,
            'errors': self.statistics.errors,
            'warnings': self.statistics.warnings
        }


class ParallelGroupAnalyzer:
    """并行组分析器 - 分析模块依赖关系并分配并行组"""

    def __init__(self, modules: List[ModuleData]):
        self.modules = modules
        self.dependency_graph = {}
        self.parallel_groups = []

    def analyze_parallel_groups(self) -> None:
        """分析并分配并行组"""
        # 1. 构建依赖图
        self._build_dependency_graph()

        # 2. 拓扑排序并分组
        self._assign_parallel_groups()

        # 3. 分配组内优先级
        self._assign_priorities()

    def _build_dependency_graph(self) -> None:
        """构建模块依赖图"""
        for module in self.modules:
            self.dependency_graph[module.name] = {
                'module': module,
                'dependencies': module.dependencies.copy(),
                'dependents': []
            }

        # 构建反向依赖关系
        for module_name, info in self.dependency_graph.items():
            for dep in info['dependencies']:
                if dep in self.dependency_graph:
                    self.dependency_graph[dep]['dependents'].append(module_name)

    def _assign_parallel_groups(self) -> None:
        """分配并行组"""
        visited = set()
        current_group = 0

        while len(visited) < len(self.modules):
            # 找到当前可以并行初始化的模块（没有未处理的依赖）
            current_group_modules = []

            for module_name, info in self.dependency_graph.items():
                if module_name in visited:
                    continue

                # 检查是否所有依赖都已处理
                unresolved_deps = [dep for dep in info['dependencies']
                                 if dep in self.dependency_graph and dep not in visited]

                if not unresolved_deps:
                    current_group_modules.append(info['module'])

            # 分配当前组
            for module in current_group_modules:
                module.parallel_group = current_group
                visited.add(module.name)

            if current_group_modules:
                self.parallel_groups.append(current_group_modules)
                current_group += 1
            else:
                # 防止无限循环（循环依赖情况）
                break

    def _assign_priorities(self) -> None:
        """分配组内优先级"""
        for group_modules in self.parallel_groups:
            # 按模块名称排序以确保确定性
            group_modules.sort(key=lambda m: m.name)

            # 分配优先级（可以根据更复杂的规则调整）
            for i, module in enumerate(group_modules):
                module.priority = len(group_modules) - i  # 字母序靠前的优先级更高

    def get_initialization_plan(self) -> Dict[str, Any]:
        """获取初始化计划"""
        return {
            'total_groups': len(self.parallel_groups),
            'groups': [
                {
                    'group_id': i,
                    'modules': [
                        {
                            'name': m.name,
                            'priority': m.priority,
                            'dependencies': m.dependencies
                        } for m in group
                    ]
                } for i, group in enumerate(self.parallel_groups)
            ]
        }
