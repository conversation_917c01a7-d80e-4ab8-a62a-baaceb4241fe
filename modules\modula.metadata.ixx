/**
 * @file modula.metadata.ixx
 * @brief Module metadata structures and utilities
 *
 * Provides core metadata structures and utility functions for
 * system-generated module metadata:
 *   - Compile-time metadata generation
 *   - Build-time JSON metadata integration
 *
 * @version 1.0.0
 */

module;

#include <algorithm>
#include <array>
#include <chrono>
#include <concepts>
#include <span>
#include <string_view>
#include <type_traits>
#include <stdexcept>

export module modula.metadata;

import modula.types;

export namespace modula {

/**
 * @brief Compile-time static metadata structure
 *
 * Contains metadata that can be determined at compile time.
 */
struct Metadata
{
    std::string_view name;            ///< Module name
    std::string_view target_name;     ///< CMake target name
    std::string_view version;         ///< System-derived version
    std::string_view directory;       ///< Module root directory path
    std::string_view primary_file;    ///< Primary module interface file
    std::string_view build_timestamp; ///< Build generation timestamp

    std::span<const std::string_view> dependencies; ///< Module dependencies list
    std::span<const std::string_view> source_files; ///< All source files in module

    int parallel_group = 0;               ///< Parallel initialization group (0 = sequential)
    int parallel_priority = 0;                     ///< Priority within parallel group (higher = first)

    // std::string_view type_name;           ///< C++ type name extracted at compile time
    // std::string_view compiler_info;       ///< Compiler version and standard information
    // std::source_location source_location; ///< Source code location

    /**
     * @brief 默认构造函数
     */
    constexpr Metadata() noexcept = default;

    /**
     * @brief 完整构造函数
     * @param name_ 模块名称
     * @param version_ 版本信息
     * @param directory_ 模块目录路径
     * @param primary_file_ 主文件路径
     * @param dependencies_ 依赖列表
     * @param source_files_ 源文件列表
     * @param build_timestamp_ 构建时间戳
     */
    constexpr Metadata(
        std::string_view name_,
        std::string_view target_name_,
        std::string_view version_,
        std::string_view directory_,
        std::string_view primary_file_,
        std::string_view build_timestamp_,
        std::span<const std::string_view> dependencies_,
        std::span<const std::string_view> source_files_,
        int parallel_group_ = 0,
        int parallel_priority_ = 0) noexcept
        : name(name_)
        , target_name(target_name_)
        , version(version_)
        , directory(directory_)
        , primary_file(primary_file_)
        , build_timestamp(build_timestamp_)
        , dependencies(dependencies_)
        , source_files(source_files_)
        , parallel_group(parallel_group_)
        , parallel_priority(parallel_priority_) {}

    /**
     * @brief Validate static metadata completeness
     * @return true if metadata contains minimum required information
     */
    [[nodiscard]] constexpr bool is_valid() const noexcept {
        return !name.empty() && !version.empty() && !primary_file.empty();
    }

    /**
     * @brief 获取依赖数量
     * @return 依赖数量
     */
    [[nodiscard]] constexpr std::size_t dependency_count() const noexcept {
        return dependencies.size();
    }

    /**
     * @brief 获取源文件数量
     * @return 源文件数量
     */
    [[nodiscard]] constexpr std::size_t source_count() const noexcept {
        return source_files.size();
    }

    /**
     * @brief 检查是否有依赖
     * @return 如果有依赖返回 true
     */
    [[nodiscard]] constexpr bool has_dependencies() const noexcept {
        return !dependencies.empty();
    }
};

/**
 * @brief 元数据注册表模板类
 *
 * 参考 UserDataRegistry 设计模式，提供编译时静态元数据注册机制。
 * 每个模块类型都有独立的注册表实例，确保类型安全。
 *
 * @tparam T 模块类型，必须满足 Module concept
 */
template<typename T>
class MetadataRegistry
{
private:
    static inline Metadata metadata_{};
    static inline bool registered_ = false;
    static inline std::chrono::steady_clock::time_point registration_time_{};

public:
    /**
     * @brief 获取注册的元数据
     * @return 元数据的常量引用
     * @throws std::runtime_error 如果元数据未注册
     */
    [[nodiscard]] static const Metadata& get_metadata() {
        if (!registered_) {
            // throw std::runtime_error("Module metadata not registered. Ensure the module's .gen.cpp file is included in the build and linked with whole archive.");
        }
        return metadata_;
    }

    /**
     * @brief 注册元数据
     * @param metadata 要注册的元数据
     * @return 如果注册成功返回 true，如果已注册返回 false
     */
    static bool register_metadata(const Metadata& metadata) noexcept {
        if (registered_) {
            return false;
        }
        metadata_ = metadata;
        registered_ = true;
        registration_time_ = std::chrono::steady_clock::now();
        return true;
    }

    /**
     * @brief 检查是否已注册
     * @return 如果已注册返回 true
     */
    [[nodiscard]] static bool is_registered() noexcept {
        return registered_;
    }

    /**
     * @brief 获取注册时间
     * @return 注册时间点
     */
    [[nodiscard]] static std::chrono::steady_clock::time_point registration_time() noexcept {
        return registration_time_;
    }

    /**
     * @brief 重置注册状态（主要用于测试）
     */
    static void reset() noexcept {
        registered_ = false;
        metadata_ = Metadata{};
        registration_time_ = {};
    }
};

/**
 * @brief Module type traits for compile-time type information extraction
 * @tparam T Module type, must satisfy Module concept
 */
template<Module T>
struct module_traits
{
    /**
     * @brief Extract type name at compile time
     *
     * Uses compiler-specific macros to extract type names.
     * Supports MSVC, GCC, and Clang.
     *
     * @return Extracted type name
     */
    [[nodiscard]] static consteval std::string_view name() noexcept {
#ifdef _MSC_VER
        constexpr std::string_view signature = __FUNCSIG__;
        constexpr auto class_pos = signature.find("class ");
        if constexpr (class_pos != std::string_view::npos) {
            constexpr auto template_start = signature.find('<', class_pos);
            if constexpr (template_start != std::string_view::npos) {
                constexpr auto type_start = signature.find("class ", template_start);
                if constexpr (type_start != std::string_view::npos) {
                    constexpr auto start = type_start + 6;
                    constexpr auto end = signature.find('>', start);
                    if constexpr (end != std::string_view::npos) {
                        constexpr auto full_name = signature.substr(start, end - start);
                        return extract_class_name(full_name);
                    }
                }
            }
        }
#elif defined(__GNUC__) || defined(__clang__)
        constexpr std::string_view signature = __PRETTY_FUNCTION__;
        constexpr auto with_pos = signature.find("with T = ");
        if constexpr (with_pos != std::string_view::npos) {
            constexpr auto start = with_pos + 9;
            constexpr auto end = signature.find_first_of(";]", start);
            if constexpr (end != std::string_view::npos) {
                constexpr auto full_name = signature.substr(start, end - start);
                return extract_class_name(full_name);
            }
        } else {
            constexpr auto t_pos = signature.find("T = ");
            if constexpr (t_pos != std::string_view::npos) {
                constexpr auto start = t_pos + 4;
                constexpr auto end = signature.find_first_of(";]", start);
                if constexpr (end != std::string_view::npos) {
                    constexpr auto full_name = signature.substr(start, end - start);
                    return extract_class_name(full_name);
                }
            }
        }
#endif
        return "UnknownModule";
    }

    /**
     * @brief Get compiler information
     * @return Compiler information string
     */
    [[nodiscard]] static consteval std::string_view compiler_info() noexcept {
#ifdef _MSC_VER
#if _MSC_VER >= 1930
        return "MSVC 2022+";
#elif _MSC_VER >= 1920
        return "MSVC 2019";
#elif _MSC_VER >= 1910
        return "MSVC 2017";
#else
        return "MSVC Legacy";
#endif
#elif defined(__clang__)
#if __clang_major__ >= 16
        return "Clang 16+";
#elif __clang_major__ >= 14
        return "Clang 14-15";
#elif __clang_major__ >= 12
        return "Clang 12-13";
#else
        return "Clang Legacy";
#endif
#elif defined(__GNUC__)
#if __GNUC__ >= 12
        return "GCC 12+";
#elif __GNUC__ >= 10
        return "GCC 10-11";
#elif __GNUC__ >= 8
        return "GCC 8-9";
#else
        return "GCC Legacy";
#endif
#else
        return "Unknown";
#endif
    }

    /**
     * @brief Get C++ standard version number
     * @return C++ standard version number
     */
    [[nodiscard]] static consteval int cpp_standard() noexcept {
        return __cplusplus;
    }

    /**
     * @brief Get C++ standard name
     * @return C++ standard name
     */
    [[nodiscard]] static consteval std::string_view cpp_standard_name() noexcept {
        if constexpr (__cplusplus >= 202302L) {
            return "C++23";
        } else if constexpr (__cplusplus >= 202002L) {
            return "C++20";
        } else if constexpr (__cplusplus >= 201703L) {
            return "C++17";
        } else if constexpr (__cplusplus >= 201402L) {
            return "C++14";
        } else if constexpr (__cplusplus >= 201103L) {
            return "C++11";
        } else {
            return "Pre-C++11";
        }
    }

private:
    /**
     * @brief Extract class name from full type name
     * @param full_name Full type name
     * @return Simplified class name
     */
    [[nodiscard]] static consteval std::string_view extract_class_name(std::string_view full_name) noexcept {
        // Remove leading/trailing spaces
        while (!full_name.empty() && full_name.front() == ' ') {
            full_name.remove_prefix(1);
        }
        while (!full_name.empty() && full_name.back() == ' ') {
            full_name.remove_suffix(1);
        }

        // Find last namespace separator
        if (const auto last_colon = full_name.rfind("::"); last_colon != std::string_view::npos) {
            full_name = full_name.substr(last_colon + 2);
        }

        // Remove template parameters
        if (const auto template_start = full_name.find('<'); template_start != std::string_view::npos) {
            full_name = full_name.substr(0, template_start);
        }

        return full_name.empty() ? "UnknownModule" : full_name;
    }
};

/**
 * @brief 获取模块元数据的便捷函数
 *
 * 提供函数式接口访问元数据注册表
 *
 * @tparam T 模块类型
 * @return 模块元数据的常量引用
 */
template<typename T>
[[nodiscard]] const Metadata& get_metadata() noexcept {
    return MetadataRegistry<T>::get_metadata();
}

} // namespace modula
