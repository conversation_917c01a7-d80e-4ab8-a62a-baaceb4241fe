#!/usr/bin/env python3
"""
Modula Generator - Command Line Entry Point

Simple command line interface for C++ metadata generation.
"""

import sys
import argparse
from pathlib import Path

from .generator import ModulaGenerator
from .config import Config
from .core import ModulaGeneratorError


def create_parser() -> argparse.ArgumentParser:
    """Create command line argument parser"""
    parser = argparse.ArgumentParser(
        description="Modula Generator - C++ Metadata Generation Tool",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )

    parser.add_argument('--json-file', required=True,
                       help='Input JSON metadata file')
    parser.add_argument('--output-dir', required=True,
                       help='Output directory for generated files')
    parser.add_argument('--verbose', action='store_true',
                       help='Enable verbose output')
    parser.add_argument('--version', action='version',
                       version='Modula Generator 1.0.0')

    return parser


def main() -> int:
    """Main function"""
    try:
        # Parse command line arguments
        parser = create_parser()
        args = parser.parse_args()

        # Validate input file
        json_file = Path(args.json_file)
        if not json_file.exists():
            print(f"[ERROR] JSON file not found: {json_file}")
            return 1

        # Create output directory
        output_dir = Path(args.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        # Create configuration
        config = Config(
            json_file=str(json_file),
            output_directory=str(output_dir),
            verbose=args.verbose
        )

        # Generate code
        generator = ModulaGenerator()
        success = generator.generate(config)

        if success:
            print("[SUCCESS] Code generation completed successfully")
            return 0
        else:
            print("[ERROR] Code generation failed")
            return 1

    except ModulaGeneratorError as e:
        print(f"[ERROR] Generation failed: {e}")
        return 1
    except KeyboardInterrupt:
        print("\n[CANCELLED] Operation cancelled by user")
        return 130
    except Exception as e:
        print(f"[ERROR] Unexpected error: {e}")
        return 1


def console_entry():
    """Console entry point for setuptools"""
    sys.exit(main())


if __name__ == "__main__":
    sys.exit(main())