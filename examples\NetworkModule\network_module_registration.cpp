/**
 * @file network_module_registration.cpp
 * @brief NetworkModule注册实现
 */

#include <iostream>

import modula.registry;
import NetworkModule;

namespace {
    struct NetworkModuleRegistration {
        NetworkModuleRegistration() {
            try {
                std::cout << "[NetworkModule] 正在注册到模块系统..." << std::endl;
                // 暂时注释掉直接注册调用，改为通过生成的代码自动注册
                // bool success = modula::register_module<NetworkModule>();
                std::cout << "[NetworkModule] 将通过自动注册器完成注册" << std::endl;
                // if (success) {
                //     std::cout << "[NetworkModule] 注册完成" << std::endl;
                // } else {
                //     std::cerr << "[NetworkModule] 注册失败 - 模块可能已存在" << std::endl;
                // }
            } catch (const std::exception& e) {
                std::cerr << "[NetworkModule] 注册失败: " << e.what() << std::endl;
            }
        }
    };
    
    static NetworkModuleRegistration network_module_registration;
}
