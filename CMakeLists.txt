# ==============================================================================
# CMakeLists.txt - Modula Framework Build Configuration
# ==============================================================================
#
# Modern C++ modular framework library.
# Provides a module management system with automatic dependency
# resolution and metadata generation.

cmake_minimum_required(VERSION 3.28)

# ==============================================================================
# Project Definition
# ==============================================================================

project(Modula
    VERSION 1.0.0
    DESCRIPTION "Modern C++ modular framework library"
    HOMEPAGE_URL "https://github.com/Z-W-M/modula"
    LANGUAGES CXX
)

# ==============================================================================
# Build Options
# ==============================================================================

option(MODULA_BUILD_EXAMPLES "Build examples" OFF)
option(MODULA_BUILD_TESTS "Build tests" OFF)
option(MODULA_INSTALL "Enable installation" ON)
option(MODULA_ENABLE_GENERATOR "Enable metadata generator component" ON)

# ==============================================================================
# CMake Configuration
# ==============================================================================

# Standard CMake modules
include(GNUInstallDirs)
include(CMakePackageConfigHelpers)

# Add custom CMake modules to module path
list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_SOURCE_DIR}/cmake")

# ==============================================================================
# Core Framework Components
# ==============================================================================

# Load components in dependency order
include(ModulaConfiguration)    # Configuration management system
include(ModulaCompiler)         # Compiler and C++ standard setup
include(ModulaTargets)          # Main library target definitions
include(ModulaDeclaration)      # Module declaration framework

# ==============================================================================
# Subdirectories
# ==============================================================================

if(MODULA_BUILD_EXAMPLES)
    add_subdirectory(examples)
endif()

# ==============================================================================
# Installation Configuration
# ==============================================================================

include(ModulaInstall)
