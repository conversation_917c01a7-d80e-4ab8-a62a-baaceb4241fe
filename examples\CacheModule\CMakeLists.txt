# CacheModule CMakeLists.txt
add_library(CacheModule)
set_target_properties(CacheModule PROPERTIES VERSION 1.0.0)

target_sources(CacheModule
    PUBLIC FILE_SET CXX_MODULES FILES CacheModule.module.ixx
    PRIVATE cache_module_registration.cpp
)

target_link_libraries(CacheModule PUBLIC modula CoreModule)
declare_module(CacheModule)

set_target_properties(CacheModule PROPERTIES
    CXX_STANDARD 23 CXX_STANDARD_REQUIRED ON CXX_EXTENSIONS OFF
)

if(CMAKE_CXX_COMPILER_ID STREQUAL "MSVC")
    target_compile_options(CacheModule PRIVATE /W4 /permissive- /utf-8 /std:c++latest /experimental:module)
    target_compile_definitions(CacheModule PRIVATE UNICODE _UNICODE _CRT_SECURE_NO_WARNINGS NOMINMAX)
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
    target_compile_options(CacheModule PRIVATE -Wall -Wextra -std=c++23 -fmodules-ts)
endif()

target_compile_definitions(CacheModule PRIVATE MODULA_CXX_STANDARD=23 MODULA_MODULES_AVAILABLE=1)
