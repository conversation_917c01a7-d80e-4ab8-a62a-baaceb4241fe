# Modular - 现代化C++20/23模块系统

`Modula` 是一个基于C++20的现代化模块化框架，用于架构设计、分析和管理复杂的应用程序，它通过将软件视为一系列定义明确、相互独立的模块(Module)集合，为构建健壮、可扩展的系统提供基础。

`Modula` 的核心哲学是为高层级的**架构分析**和**运行时生命周期管理**提供工具，而非干涉底层的编译模型。

一项至关重要的区别： Modula中的Module (模块) 是一个软件架构层面的概念。它代表您应用程序中一个逻辑上的、高层级的组件（例如：一个子系统、一项服务、一个主功能库）。此概念完全独立于C++20语言层级的 modules (即 import/export 语法)，且两者毫无关系。无论您使用传统头文件还是C++20模块进行编译，Modula 都能帮助您管理您的软件架构。

1. **模块 (The Module)**：`Module` 是 `Modula` 应用程序中的基本构建块。它是一个自包含、可替换的组件，拥有明确定义的公共API和生命周期。通常，一个`Module`对应一个CMake目标 (target)（例如一个库）。
2. **声明式架构 (Declarative Architecture)**：在CMake构建系统中将目标声明为`Module`。此行为会将它们注册到`Modula`自动分析框架中。
3. **自动化分析 (Automated Analysis)**：在CMake配置阶段，`Modula`会检查这些`Module`，分析它们之间的相互依赖关系，并发现它们的公共API。
4. **元数据生成 (Metadata Generation)**：分析过程会生成元数据文件。
5. **模块化注册**：通过ModuleRegistry注册模块
6. **智能化运行时**：在运行时，`Modula`的`ModuleManager`会已注册的模块：
*   构建一个依赖关系图。
*   探测循环依赖。
*   以正确的拓扑顺序初始化所有的`Module`。
*   以相反的顺序提供受控的关闭流程。

## 核心功能
1. **模块定义规范:** 模块是程序中一个独立、可替换的单元, 具有明确定义的接口和功能.成为模块需要满足以下条件
    - 通过`ModularFramework.cmake`中的`enable_modular_framework()`函数在构建系统中声明模块库
    - 模块库中需要包含一个主模块文件(`.ixx`或`.h`格式, 命名为`<目录名>.module.ixx`或`<目录名>_module.h`), 主模块文件作为模块的导出接口 (使用C++20 `export module`语法)
    - 主模块文件必须实现符合概念约束(`modular.concepts concept Module`)的生命周期管理类, 称之为主模块类
    - 主模块类需要在编译期或运行时显式注册(通过`modular.registry`)
    
2. **模块元数据生成**
    - **CMake编译期信息处理**: 通过`ModularFramework.cmake`自动分析模块结构, 输出运行时可加载的依赖元数据 (JSON/二进制格式) 
    - **现代C++元数据生成技术**实现**编译期元数据生成**(`modular.metadata`)
        - 编译期静态反射(未来实现)和类型萃取
        - 属性标注系统 (`[[modular::metadata]]`)(未来实现)
        - `consteval`和模板元编程
        - 统宏定义: 通过`REGISTER_MODULE`宏注册
    - 自动生成module的元信息(name version dependency等), 而减少显示声明
    
3. **模块生命周期管理 (`ModuleManager`)**
    - **自动化初始化**: 按依赖顺序自动执行模块初始化和卸载
    - **热加载支持**: (未来实现)支持模块的热加载和热重载, 跨平台的动态模块加载器, 支持动态库 (Windows (.dll), Linux  (.so) 和macOS  (.dylib) ) 的加载
    - **并发支持**: (未来实现)支持模块的并发初始化以提升性能
    - **超时控制**: 防止模块初始化阻塞, 提供超时机制
    - **性能监控**: 详细的模块性能统计和资源监控
    - **错误处理**: 细粒度错误报告和故障恢复策略
    - **故障恢复**: 异常安全的回滚机制
    - **智能依赖解析**: 基于依赖图构建有向无环图(DAG), 使用Kahn/BFS算法生成初始化序列和循环依赖检测(`modular.dependency_graph`)

## 技术亮点

- **C++20/23模块系统**: 完全基于现代C++模块, 支持`export module`和`import`语法
- **编译期元编程**: 使用`consteval`、`concepts`和模板元编程实现零运行时开销
- **非侵入式CMake集成**: 通过`enable_modular_framework()`实现自动模块发现
- **多格式元数据输出**: 支持JSON、CMake、DOT格式的元数据和依赖图生成
- **智能依赖分析**: 自动分析`import`语句和链接关系, 构建完整依赖图
- **属性标注系统**: 支持`[[modular::metadata]]`属性进行编译期元数据标注
- **异常安全设计**: 完整的错误处理和资源管理机制

## 系统要求

- **C++20/23兼容编译器**: 
  - GCC 15+ (推荐用于模块支持)
  - Clang 16+
  - MSVC 2022+ (Visual Studio 17.0+)
- **CMake 3.28+**: 支持C++模块的实验性API
- **操作系统**: Windows 10+, Linux (Ubuntu 20.04+), macOS 12+

## 模块职责分配

| 模块 | 核心职责 | 主要组件 | 
|------------------------------|------------|--------------------------|
| **modular**                  | 框架入口    | 统一导入, 全局配置 |
| **modular.types**            | 基础类型定义 | `ModuleInfo`, `IModule`, `ModuleState` |
| **modular.concepts**         | 概念和约束  | `Module`概念, `module_traits`, 属性系统 |
| **modular.metadata**         | 元数据管理  | `MetadataManager`, 编译期元数据生成 |
| **modular.registry**         | 注册管理    | `ModuleRegistry`, `REGISTER_MODULE`宏 |
| **modular.dependency_graph** | 依赖分析    | 拓扑排序, 循环依赖检测 |
| **modular.manager**          | 生命周期管理 | `ModuleManager`, 初始化序列控制 |
| **modular.info**             | 模块信息管理 | `InfoManager`, 用户手动注册 |
| **modular.metadata_loader**  | 元数据加载器 | `MetadataLoader`, 自动加载JSON元数据 |

