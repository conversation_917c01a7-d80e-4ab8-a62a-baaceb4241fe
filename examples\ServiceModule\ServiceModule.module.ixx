/**
 * @file ServiceModule.module.ixx
 * @brief ServiceModule - 服务模块，依赖DatabaseModule和NetworkModule
 */

module;
#include <iostream>
#include <string>
#include <chrono>
#include <thread>
#include <vector>

export module ServiceModule;
import DatabaseModule;
import NetworkModule;

export class ServiceModule {
public:
    ServiceModule() = default;
    ~ServiceModule() { if (initialized_) shutdown(); }

    bool initialize() {
        if (initialized_) return true;
        std::cout << "[ServiceModule] 开始初始化业务服务..." << std::endl;
        initialization_time_ = std::chrono::steady_clock::now();
        std::this_thread::sleep_for(std::chrono::milliseconds(220));
        initialized_ = true;
        std::cout << "[ServiceModule] 业务服务初始化完成" << std::endl;
        return true;
    }

    void shutdown() {
        if (!initialized_) return;
        std::cout << "[ServiceModule] 关闭业务服务..." << std::endl;
        std::this_thread::sleep_for(std::chrono::milliseconds(90));
        initialized_ = false;
        std::cout << "[ServiceModule] 业务服务关闭完成" << std::endl;
    }

    bool is_initialized() const noexcept { return initialized_; }
    std::string get_version() const { return "1.0.0"; }
    std::vector<std::string> get_dependencies() const { return {"DatabaseModule", "NetworkModule"}; }
    bool supports_hot_reload() const noexcept { return false; }
    auto get_initialization_time() const noexcept { return initialization_time_; }

    void process_request(const std::string& request) {
        if (!initialized_) throw std::runtime_error("ServiceModule not initialized");
        std::cout << "[ServiceModule] 处理业务请求: " << request << std::endl;
    }

    bool is_service_ready() const noexcept { return initialized_; }

private:
    bool initialized_ = false;
    std::chrono::steady_clock::time_point initialization_time_;
};
