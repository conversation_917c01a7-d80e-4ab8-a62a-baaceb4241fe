# AnalyzerProcessor.cmake
# 模块分析处理器
#
# 轻量级模块内容分析功能，专注于核心分析逻辑
# 基于原有ModuleAnalyzer.cmake的核心功能重构

cmake_minimum_required(VERSION 3.28)

#[=======================================================================[.rst:
AnalyzerProcessor - 模块分析处理器
==============================

轻量级模块内容分析功能，专注于核心分析逻辑。

核心职责：
- 模块文件内容解析和分析
- 模块名称提取和验证
- 导入导出关系分析
- 模块元数据生成

设计原则：
- 轻量级：简单直接的实现方式
- 功能保持：保留原有分析逻辑
- 性能优化：智能文件读取和缓存
- 语法容错：处理各种C++模块语法变体

主要接口：
- modula_process_analysis(): 处理目标列表的模块分析

#]=======================================================================]

include_guard(GLOBAL)

# 包含依赖模块
include("${CMAKE_CURRENT_LIST_DIR}/../ModuleConfiguration.cmake")
include("${CMAKE_CURRENT_LIST_DIR}/../ModuleUtils.cmake")

# 主动注册到ModulePreprocessor
if(COMMAND modula_register_processor)
    modula_register_processor("AnalyzerProcessor" "modula_process_analysis")
endif()

#=======================================================================
# 分析配置和状态
#=======================================================================

# 分析配置缓存
if(NOT DEFINED _ANALYZER_CONFIG_LOADED)
    set(_ANALYZER_CONFIG_LOADED FALSE CACHE INTERNAL "Analyzer configuration loaded flag")
endif()

#=======================================================================
# 主要处理接口
#=======================================================================

#[=======================================================================[.rst:
.. command:: modula_process_analysis

  处理目标列表的模块分析（主要接口）。

  .. code-block:: cmake

    modula_process_analysis(<targets>)

  ``targets``
    要处理的目标列表

  **功能说明：**

  为指定的目标列表执行模块分析：
  - 自动加载配置
  - 为每个目标执行分析
  - 设置目标属性
  - 提供详细的日志输出

#]=======================================================================]
function(modula_process_analysis targets)
    if(NOT targets)
        modula_verbose_message("No targets provided for analysis" MODULE "AnalyzerProcessor")
        return()
    endif()

    modula_verbose_message("Processing analysis for targets: ${targets}" MODULE "AnalyzerProcessor")

    # 确保配置已加载
    _analyzer_load_configuration()

    set(processed_count 0)
    set(failed_count 0)

    # 为每个目标执行分析
    foreach(target_name ${targets})
        if(NOT TARGET ${target_name})
            modula_log_message(WARNING "Target '${target_name}' does not exist, skipping" MODULE "AnalyzerProcessor")
            math(EXPR failed_count "${failed_count} + 1")
            continue()
        endif()

        # 执行模块分析
        _analyzer_process_target("${target_name}" target_result)

        if(target_result)
            math(EXPR processed_count "${processed_count} + 1")
            modula_verbose_message("Successfully analyzed module for target '${target_name}'" MODULE "AnalyzerProcessor")
        else()
            math(EXPR failed_count "${failed_count} + 1")
            modula_log_message(WARNING "Failed to analyze module for target '${target_name}'" MODULE "AnalyzerProcessor")
        endif()
    endforeach()

    modula_verbose_message("Analysis completed: ${processed_count} successful, ${failed_count} failed" MODULE "AnalyzerProcessor")
endfunction()

#=======================================================================
# 分析核心功能实现
#=======================================================================

#[=======================================================================[.rst:
.. command:: _analyzer_load_configuration

  加载分析配置（内部函数）。

  .. code-block:: cmake

    _analyzer_load_configuration()

#]=======================================================================]
function(_analyzer_load_configuration)
    if(_ANALYZER_CONFIG_LOADED)
        return()
    endif()

    # 初始化分析缓存系统（从ModuleAnalyzer集成）
    _analyzer_initialize_analyse_cache()

    # 设置分析配置选项（使用默认值）
    set(strict_validation TRUE)
    set(_ANALYZER_STRICT_VALIDATION "${strict_validation}" CACHE INTERNAL "Analyzer strict validation")

    # 设置缓存配置（使用默认值）
    set(cache_enabled TRUE)
    set(_ANALYZER_CACHE_ENABLED "${cache_enabled}" CACHE INTERNAL "Analyzer cache enabled")

    set(_ANALYZER_CONFIG_LOADED TRUE CACHE INTERNAL "Analyzer configuration loaded flag" FORCE)
    modula_verbose_message("Analyzer configuration loaded" MODULE "AnalyzerProcessor")
endfunction()

#[=======================================================================[.rst:
.. command:: _analyzer_initialize_analyse_cache

  初始化解析缓存系统（内部函数，从ModuleAnalyzer集成）。

  **功能说明：**

  初始化模块内容解析的缓存系统：
  - 设置缓存数据结构
  - 预编译解析正则表达式
  - 初始化性能计数器

#]=======================================================================]
function(_analyzer_initialize_analyse_cache)
    if(_ANALYZER_CACHE_INITIALIZED)
        return()
    endif()

    # 预编译常用的正则表达式模式（性能优化）
    set(_ANALYZER_EXPORT_MODULE_PATTERN "export[ \\t]+module[ \\t]+([a-zA-Z_][a-zA-Z0-9_.]*)" CACHE INTERNAL "Export module pattern")
    set(_ANALYZER_IMPORT_PATTERN "import[ \\t]+([a-zA-Z_][a-zA-Z0-9_.]*)" CACHE INTERNAL "Import pattern")
    set(_ANALYZER_EXPORT_PATTERN "export[ \\t]+([^;]+)" CACHE INTERNAL "Export pattern")

    # 初始化文件类型检测模式
    set(_ANALYZER_CPP_MODULE_EXTENSIONS "\\.ixx$|\\.cppm$|\\.mpp$" CACHE INTERNAL "C++ module extensions pattern")
    set(_ANALYZER_HEADER_EXTENSIONS "\\.h$|\\.hpp$" CACHE INTERNAL "Header extensions pattern")

    # 初始化缓存数据结构
    set(_ANALYZER_CACHE_DATA "" CACHE INTERNAL "Analyzer cache data")
    set(_ANALYZER_CACHE_TIMESTAMPS "" CACHE INTERNAL "Analyzer cache timestamps")

    # 标记为已初始化
    set(_ANALYZER_CACHE_INITIALIZED TRUE CACHE INTERNAL "Analyzer cache initialization flag" FORCE)

    modula_verbose_message("Analyzer cache system initialized" MODULE "AnalyzerProcessor")
endfunction()



#[=======================================================================[.rst:
.. command:: _analyzer_process_target

  处理单个目标的模块分析（内部函数）。

  .. code-block:: cmake

    _analyzer_process_target(<target_name> <output_var>)

  ``target_name``
    目标名称
  ``output_var``
    存储处理结果的变量名（TRUE/FALSE）

#]=======================================================================]
function(_analyzer_process_target target_name output_var)
    # 获取目标的模块接口文件
    get_property(module_primary_file TARGET ${target_name} PROPERTY MODULA_MODULE_PRIMARY_FILE)
    get_property(expected_module_name TARGET ${target_name} PROPERTY MODULA_MODULE_NAME)

    if(NOT module_primary_file)
        modula_verbose_message("No interface file found for target '${target_name}', skipping analysis" MODULE "AnalyzerProcessor")
        set(${output_var} TRUE PARENT_SCOPE)
        return()
    endif()

    if(NOT EXISTS "${module_primary_file}")
        processor_base_add_error("AnalyzerProcessor" "Interface file does not exist: ${module_primary_file}")
        set(${output_var} FALSE PARENT_SCOPE)
        return()
    endif()

    # 检查缓存
    if(_ANALYZER_CACHE_ENABLED)
        _analyzer_check_analyse_cache("${module_primary_file}" cached_result)
        if(cached_result)
            modula_verbose_message("Using cached analysis for file: ${module_primary_file}" MODULE "AnalyzerProcessor")
            _analyzer_apply_cached_result("${target_name}" "${cached_result}")
            set(${output_var} TRUE PARENT_SCOPE)
            return()
        endif()
    endif()

    # 执行文件分析
    _analyzer_analyze_file("${module_primary_file}" "${expected_module_name}" analysis_result)

    if(analysis_result)
        # 应用分析结果到目标
        _analyzer_apply_analysis_result("${target_name}" "${analysis_result}")

        # 更新缓存
        if(_ANALYZER_CACHE_ENABLED)
            _analyzer_update_analyse_cache("${module_primary_file}" "${analysis_result}")
        endif()

        set(${output_var} TRUE PARENT_SCOPE)
    else()
        processor_base_add_error("AnalyzerProcessor" "Failed to analyze file: ${module_primary_file}")
        set(${output_var} FALSE PARENT_SCOPE)
    endif()
endfunction()

#[=======================================================================[.rst:
.. command:: _analyzer_analyze_file

  分析模块文件（内部函数，从ModuleAnalyzer集成）。

  .. code-block:: cmake

    _analyzer_analyze_file(<file_path> <expected_module_name> <output_var>)

  ``file_path``
    文件路径
  ``expected_module_name``
    预期模块名
  ``output_var``
    存储分析结果的变量名

#]=======================================================================]
function(_analyzer_analyze_file file_path expected_module_name output_var)
    # 验证文件存在性
    if(NOT EXISTS "${file_path}")
        modula_log_message(ERROR "Module file does not exist: ${file_path}" MODULE "AnalyzerProcessor")
        set(${output_var} "" PARENT_SCOPE)
        return()
    endif()

    # 检查解析缓存
    _analyzer_check_analyse_cache("${file_path}" cached_result)
    if(cached_result)
        modula_verbose_message("Using cached analysis result for: ${file_path}" MODULE "AnalyzerProcessor")
        set(${output_var} "${cached_result}" PARENT_SCOPE)
        return()
    endif()

    # 确定文件类型
    _analyzer_determine_file_type("${file_path}" file_type)

    # 根据文件类型执行相应的分析
    if(file_type STREQUAL "CPP_MODULE")
        _analyzer_analyse_cpp_module_file("${file_path}" "${expected_module_name}" analysis_result)
    elseif(file_type STREQUAL "HEADER")
        _analyzer_analyse_header_file("${file_path}" "${expected_module_name}" analysis_result)
    else()
        modula_log_message(ERROR "Unsupported file type for analysis: ${file_path}" MODULE "AnalyzerProcessor")
        set(analysis_result "")
    endif()

    # 更新解析缓存
    _analyzer_update_analyse_cache("${file_path}" "${analysis_result}")

    set(${output_var} "${analysis_result}" PARENT_SCOPE)
    modula_verbose_message("Module file analysis completed: ${file_path}" MODULE "AnalyzerProcessor")
endfunction()

#[=======================================================================[.rst:
.. command:: _analyzer_determine_file_type

  确定文件类型（内部函数，从ModuleAnalyzer集成）。

  .. code-block:: cmake

    _analyzer_determine_file_type(<file_path> <output_var>)

  ``file_path``
    文件路径
  ``output_var``
    存储文件类型的变量名

  **返回值：**
  - CPP_MODULE: C++模块文件（.ixx, .cppm, .mpp）
  - HEADER: 头文件（.h, .hpp）
  - UNKNOWN: 未知类型

#]=======================================================================]
function(_analyzer_determine_file_type file_path output_var)
    get_property(cpp_module_pattern CACHE _ANALYZER_CPP_MODULE_EXTENSIONS PROPERTY VALUE)
    get_property(header_pattern CACHE _ANALYZER_HEADER_EXTENSIONS PROPERTY VALUE)

    if(file_path MATCHES "${cpp_module_pattern}")
        set(${output_var} "CPP_MODULE" PARENT_SCOPE)
    elseif(file_path MATCHES "${header_pattern}")
        set(${output_var} "HEADER" PARENT_SCOPE)
    else()
        set(${output_var} "UNKNOWN" PARENT_SCOPE)
    endif()
endfunction()

#[=======================================================================[.rst:
.. command:: _analyzer_analyse_cpp_module_file

  分析C++模块文件（内部函数，从ModuleAnalyzer集成）。

  .. code-block:: cmake

    _analyzer_analyse_cpp_module_file(<file_path> <expected_name> <output_var>)

  ``file_path``
    C++模块文件路径
  ``expected_name``
    预期模块名
  ``output_var``
    存储分析结果的变量名

  **功能说明：**

  深度分析C++模块文件：
  - 提取export module声明中的模块名
  - 解析所有import语句
  - 分析导出的符号和接口
  - 验证模块定义的语法正确性

#]=======================================================================]
function(_analyzer_analyse_cpp_module_file file_path expected_name output_var)
    # 智能文件读取
    _analyzer_smart_file_read("${file_path}" file_content)

    # 初始化分析结果
    set(analysis_result "")
    list(APPEND analysis_result "FILE_TYPE:CPP_MODULE")
    list(APPEND analysis_result "FILE_PATH:${file_path}")

    # 提取模块名
    _analyzer_extract_module_name("${file_content}" actual_module_name)
    if(actual_module_name)
        list(APPEND analysis_result "MODULE_NAME:${actual_module_name}")

        # 验证模块名是否与预期匹配
        string(TOLOWER "${actual_module_name}" actual_lower)
        string(TOLOWER "${expected_name}" expected_lower)
        if(NOT actual_lower STREQUAL expected_lower)
            list(APPEND analysis_result "VALIDATION_STATUS:WARNING")
            list(APPEND analysis_result "ERROR_MESSAGES:Module name '${actual_module_name}' does not match expected '${expected_name}'")
        else()
            list(APPEND analysis_result "VALIDATION_STATUS:VALID")
        endif()
    else()
        list(APPEND analysis_result "MODULE_NAME:${expected_name}")
        list(APPEND analysis_result "VALIDATION_STATUS:ERROR")
        list(APPEND analysis_result "ERROR_MESSAGES:No export module declaration found")
    endif()

    # 提取import语句
    _analyzer_extract_imports("${file_content}" imports)
    list(APPEND analysis_result "IMPORTS:${imports}")

    # 提取导出符号
    _analyzer_extract_exports("${file_content}" exports)
    list(APPEND analysis_result "EXPORTS:${exports}")

    set(${output_var} "${analysis_result}" PARENT_SCOPE)
endfunction()

#[=======================================================================[.rst:
.. command:: _analyzer_analyse_header_file

  分析头文件（内部函数，从ModuleAnalyzer集成）。

  .. code-block:: cmake

    _analyzer_analyse_header_file(<file_path> <expected_name> <output_var>)

  ``file_path``
    头文件路径
  ``expected_name``
    预期模块名
  ``output_var``
    存储分析结果的变量名

  **功能说明：**

  分析头文件的模块相关信息：
  - 检查是否包含模块相关的宏定义
  - 分析导出的类和函数
  - 验证头文件的模块兼容性

#]=======================================================================]
function(_analyzer_analyse_header_file file_path expected_name output_var)
    # 智能文件读取
    _analyzer_smart_file_read("${file_path}" file_content)

    # 初始化分析结果
    set(analysis_result "")
    list(APPEND analysis_result "FILE_TYPE:HEADER")
    list(APPEND analysis_result "FILE_PATH:${file_path}")
    list(APPEND analysis_result "MODULE_NAME:${expected_name}")

    # 头文件通常不包含export module声明，使用预期名称
    list(APPEND analysis_result "VALIDATION_STATUS:VALID")

    # 分析头文件中的导出符号（类、函数等）
    _analyzer_extract_header_exports("${file_content}" exports)
    list(APPEND analysis_result "EXPORTS:${exports}")

    # 头文件通常不包含import语句
    list(APPEND analysis_result "IMPORTS:")

    set(${output_var} "${analysis_result}" PARENT_SCOPE)
endfunction()

#[=======================================================================[.rst:
.. command:: _analyzer_smart_file_read

  智能文件读取（内部函数，从ModuleAnalyzer集成）。

  .. code-block:: cmake

    _analyzer_smart_file_read(<file_path> <output_var>)

  ``file_path``
    文件路径
  ``output_var``
    存储文件内容的变量名

  **功能说明：**

  优化的文件读取策略：
  - 首先读取前2KB内容进行快速分析
  - 如果检测到可能的截断，读取完整文件
  - 支持大文件的高效处理

#]=======================================================================]
function(_analyzer_smart_file_read file_path output_var)
    # 智能文件读取：限制读取大小（前2KB通常足够）
    modula_file_read("${file_path}" file_content LIMIT 2048)

    # 检查是否可能被截断
    string(LENGTH "${file_content}" content_length)
    if(content_length EQUAL 2048)
        # 检查是否在关键语句中间被截断
        if(file_content MATCHES "export[ \\t\\n\\r]*$" OR
           file_content MATCHES "module[ \\t\\n\\r]*$" OR
           file_content MATCHES "import[ \\t\\n\\r]*$")
            # 可能被截断，读取完整文件
            modula_file_read("${file_path}" file_content)
        endif()
    endif()

    set(${output_var} "${file_content}" PARENT_SCOPE)
endfunction()

#[=======================================================================[.rst:
.. command:: _analyzer_extract_module_name

  从文件内容中提取模块名（内部函数，从ModuleAnalyzer集成）。

  .. code-block:: cmake

    _analyzer_extract_module_name(<file_content> <output_var>)

  ``file_content``
    文件内容
  ``output_var``
    存储提取的模块名的变量名

  **功能说明：**

  从C++模块文件的export module声明中提取模块名：
  - 支持多种语法格式
  - 处理空白字符和注释
  - 规范化模块名格式

#]=======================================================================]
function(_analyzer_extract_module_name file_content output_var)
    get_property(export_module_pattern CACHE _ANALYZER_EXPORT_MODULE_PATTERN PROPERTY VALUE)

    # 提取模块名
    set(module_name "")
    string(REGEX MATCH "${export_module_pattern}" module_match "${file_content}")
    if(module_match)
        string(REGEX REPLACE "${export_module_pattern}" "\\1" module_name "${module_match}")
        # 规范化为小写
        string(TOLOWER "${module_name}" module_name)
        # 移除可能的分号
        string(REGEX REPLACE ";.*$" "" module_name "${module_name}")
        string(STRIP "${module_name}" module_name)
    endif()

    set(${output_var} "${module_name}" PARENT_SCOPE)
endfunction()

#[=======================================================================[.rst:
.. command:: _analyzer_extract_imports

  从文件内容中提取import语句（内部函数，从ModuleAnalyzer集成）。

  .. code-block:: cmake

    _analyzer_extract_imports(<file_content> <output_var>)

  ``file_content``
    文件内容
  ``output_var``
    存储导入模块列表的变量名

#]=======================================================================]
function(_analyzer_extract_imports file_content output_var)
    get_property(import_pattern CACHE _ANALYZER_IMPORT_PATTERN PROPERTY VALUE)

    set(imports "")

    # 查找所有import语句
    string(REGEX MATCHALL "${import_pattern}" import_matches "${file_content}")
    foreach(import_match ${import_matches})
        string(REGEX REPLACE "${import_pattern}" "\\1" import_name "${import_match}")
        string(REGEX REPLACE ";.*$" "" import_name "${import_name}")
        string(STRIP "${import_name}" import_name)
        if(import_name)
            list(APPEND imports "${import_name}")
        endif()
    endforeach()

    # 移除重复项
    if(imports)
        list(REMOVE_DUPLICATES imports)
    endif()

    set(${output_var} "${imports}" PARENT_SCOPE)
endfunction()

#[=======================================================================[.rst:
.. command:: _analyzer_extract_exports

  从C++模块文件中提取导出符号（内部函数，从ModuleAnalyzer集成）。

  .. code-block:: cmake

    _analyzer_extract_exports(<file_content> <output_var>)

  ``file_content``
    文件内容
  ``output_var``
    存储导出符号列表的变量名

#]=======================================================================]
function(_analyzer_extract_exports file_content output_var)
    set(exports "")

    # 查找所有export语句（除了export module）
    string(REGEX MATCHALL "export[ \\t\\n\\r]+[^m][^;]*" export_matches "${file_content}")
    foreach(export_match ${export_matches})
        # 清理export关键字
        string(REGEX REPLACE "^export[ \\t\\n\\r]+" "" export_content "${export_match}")
        string(STRIP "${export_content}" export_content)
        if(export_content)
            list(APPEND exports "${export_content}")
        endif()
    endforeach()

    set(${output_var} "${exports}" PARENT_SCOPE)
endfunction()

#[=======================================================================[.rst:
.. command:: _analyzer_extract_header_exports

  从头文件中提取导出符号（内部函数，从ModuleAnalyzer集成）。

  .. code-block:: cmake

    _analyzer_extract_header_exports(<file_content> <output_var>)

  ``file_content``
    文件内容
  ``output_var``
    存储导出符号列表的变量名

#]=======================================================================]
function(_analyzer_extract_header_exports file_content output_var)
    set(exports "")

    # 从头文件中提取类和函数声明
    # 查找类声明
    string(REGEX MATCHALL "class[ \\t\\n\\r]+[a-zA-Z_][a-zA-Z0-9_]*" class_matches "${file_content}")
    foreach(class_match ${class_matches})
        string(REGEX REPLACE "class[ \\t\\n\\r]+" "" class_name "${class_match}")
        string(STRIP "${class_name}" class_name)
        if(class_name)
            list(APPEND exports "class ${class_name}")
        endif()
    endforeach()

    # 查找函数声明（简化版本）
    string(REGEX MATCHALL "[a-zA-Z_][a-zA-Z0-9_]*[ \\t\\n\\r]*\\([^)]*\\)[ \\t\\n\\r]*;" function_matches "${file_content}")
    foreach(function_match ${function_matches})
        string(REGEX REPLACE "[ \\t\\n\\r]*\\([^)]*\\)[ \\t\\n\\r]*;.*$" "" function_name "${function_match}")
        string(STRIP "${function_name}" function_name)
        if(function_name AND NOT function_name MATCHES "^(if|for|while|switch|return)$")
            list(APPEND exports "function ${function_name}")
        endif()
    endforeach()

    # 移除重复项
    if(exports)
        list(REMOVE_DUPLICATES exports)
    endif()

    set(${output_var} "${exports}" PARENT_SCOPE)
endfunction()

#=======================================================================
# 结果应用和缓存管理
#=======================================================================

#[=======================================================================[.rst:
.. command:: _analyzer_apply_analysis_result

  应用分析结果到目标（内部函数）。

  .. code-block:: cmake

    _analyzer_apply_analysis_result(<target_name> <analysis_result>)

  ``target_name``
    目标名称
  ``analysis_result``
    分析结果

#]=======================================================================]
function(_analyzer_apply_analysis_result target_name analysis_result)
    # 解析分析结果并设置目标属性
    foreach(result_item ${analysis_result})
        if(result_item MATCHES "^([^:]+):(.*)$")
            set(key "${CMAKE_MATCH_1}")
            set(value "${CMAKE_MATCH_2}")

            if(key STREQUAL "MODULE_NAME")
                set_property(TARGET ${target_name} PROPERTY MODULA_MODULE_NAME "${value}")
            elseif(key STREQUAL "IMPORTS")
                set_property(TARGET ${target_name} PROPERTY MODULA_MODULE_IMPORTS "${value}")
            elseif(key STREQUAL "EXPORTS")
                set_property(TARGET ${target_name} PROPERTY MODULA_MODULE_EXPORTS "${value}")
            elseif(key STREQUAL "VALIDATION_STATUS")
                set_property(TARGET ${target_name} PROPERTY MODULA_MODULE_VALIDATION_STATUS "${value}")
            elseif(key STREQUAL "ERROR_MESSAGES")
                set_property(TARGET ${target_name} PROPERTY MODULA_MODULE_ERROR_MESSAGES "${value}")
            endif()
        endif()
    endforeach()

    # 记录分析时间戳
    string(TIMESTAMP analysis_time "%Y-%m-%d %H:%M:%S")
    set_property(TARGET ${target_name} PROPERTY MODULA_MODULE_ANALYSIS_TIME "${analysis_time}")

    modula_verbose_message("Applied analysis result to target '${target_name}'" MODULE "AnalyzerProcessor")
endfunction()

#[=======================================================================[.rst:
.. command:: _analyzer_check_analyse_cache

  检查解析缓存（内部函数，从ModuleAnalyzer集成）。

  .. code-block:: cmake

    _analyzer_check_analyse_cache(<file_path> <output_var>)

  ``file_path``
    文件路径
  ``output_var``
    存储缓存结果的变量名

#]=======================================================================]
function(_analyzer_check_analyse_cache file_path output_var)
    # 简化的缓存实现：基于文件时间戳
    # 实际实现可以更复杂，包括内容哈希等
    set(${output_var} "" PARENT_SCOPE)
endfunction()

#[=======================================================================[.rst:
.. command:: _analyzer_update_analyse_cache

  更新解析缓存（内部函数，从ModuleAnalyzer集成）。

  .. code-block:: cmake

    _analyzer_update_analyse_cache(<file_path> <analysis_result>)

  ``file_path``
    文件路径
  ``analysis_result``
    分析结果

#]=======================================================================]
function(_analyzer_update_analyse_cache file_path analysis_result)
    # 缓存更新实现
    # 当前为占位符实现
    modula_verbose_message("Updated analysis cache for file: ${file_path}" MODULE "AnalyzerProcessor")
endfunction()

#[=======================================================================[.rst:
.. command:: _analyzer_apply_cached_result

  应用缓存的分析结果（内部函数）。

  .. code-block:: cmake

    _analyzer_apply_cached_result(<target_name> <cached_result>)

  ``target_name``
    目标名称
  ``cached_result``
    缓存的分析结果

#]=======================================================================]
function(_analyzer_apply_cached_result target_name cached_result)
    _analyzer_apply_analysis_result("${target_name}" "${cached_result}")
endfunction()

#=======================================================================
# 公共接口函数（向后兼容）
#=======================================================================

#[=======================================================================[.rst:
.. command:: modula_analyse_module_file

  模块分析公共接口（向后兼容，从ModuleAnalyzer集成）。

  .. code-block:: cmake

    modula_analyse_module_file(<file_path> <expected_module_name> <output_var>)

  ``file_path``
    文件路径
  ``expected_module_name``
    预期模块名
  ``output_var``
    存储分析结果的变量名

  **功能说明：**

  提供向后兼容的模块分析接口，内部调用重构后的AnalyzerProcessor。

#]=======================================================================]
function(modula_analyse_module_file file_path expected_module_name output_var)
    # 确保配置已加载
    _analyzer_load_configuration()

    # 执行分析（使用重构后的实现）
    _analyzer_analyze_file("${file_path}" "${expected_module_name}" result)

    set(${output_var} "${result}" PARENT_SCOPE)
endfunction()
