#!/usr/bin/env python3
"""
配置和模板管理层 - Modula元数据生成器的统一配置接口

作为唯一的对外接口和入口点，包含所有模板定义、配置参数和统一的配置访问接口。
简化的配置系统，专注于辅助脚本的实际需求。

核心功能：
- 统一的配置管理
- C++代码模板定义
- 文件路径和命名规则
- 可配置的生成选项
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from datetime import datetime


@dataclass
class Config:
    """配置类"""
    # 基本配置
    json_file: Optional[str] = None
    output_directory: str = "modula_generated"
    verbose: bool = False
    generate_companion_files: bool = True  # 默认行为

    # 生成选项
    enable_cpp_generation: bool = True
    enable_json_generation: bool = False
    enable_incremental: bool = True

    # 模板变量
    custom_variables: Dict[str, Any] = field(default_factory=dict)

    def validate(self) -> List[str]:
        """验证配置"""
        errors = []

        if not self.json_file:
            errors.append("json_file must be specified")
        elif not Path(self.json_file).exists():
            errors.append(f"JSON file not found: {self.json_file}")

        if not self.output_directory:
            errors.append("output_directory cannot be empty")

        return errors


class ConfigurationManager:
    """配置管理器"""

    def __init__(self):
        self.config = Config()
        self.logger = logging.getLogger("configuration")

    def load_from_args(self, args) -> None:
        """从命令行参数加载配置"""
        if hasattr(args, 'json_file') and args.json_file:
            self.config.json_file = args.json_file

        if hasattr(args, 'output_dir') and args.output_dir:
            self.config.output_directory = args.output_dir

        if hasattr(args, 'verbose') and args.verbose:
            self.config.verbose = args.verbose

        # 从环境变量加载
        if os.getenv('MODULA_OUTPUT_DIR'):
            self.config.output_directory = os.getenv('MODULA_OUTPUT_DIR')

        if os.getenv('MODULA_VERBOSE'):
            self.config.verbose = os.getenv('MODULA_VERBOSE').lower() == 'true'

    def get_config(self) -> Config:
        """获取当前配置"""
        return self.config

    def validate_config(self) -> bool:
        """验证配置"""
        errors = self.config.validate()
        if errors:
            for error in errors:
                self.logger.error(f"Config validation error: {error}")
            return False
        return True


class TemplateRenderer:
    """模板渲染器"""

    @staticmethod
    def render_template(template: str, variables: Dict[str, Any]) -> str:
        """渲染模板"""
        from templates import LegacyTemplateAdapter
        adapter = LegacyTemplateAdapter()
        return adapter.render_template(template, variables)

    @staticmethod
    def escape_cpp_string(s: str) -> str:
        """转义C++字符串"""
        from templates import TemplateRenderer as NewTemplateRenderer
        return NewTemplateRenderer.escape_cpp_string(s)


class PathManager:
    """路径管理器"""

    @staticmethod
    def get_generated_path(output_dir: str) -> str:
        """获取全局生成文件路径"""
        return os.path.join(output_dir, "modula.generated.ixx")

    @staticmethod
    def get_companion_file_path(output_dir: str, module_name: str) -> str:
        """获取伴生文件路径"""
        return os.path.join(output_dir, f"{module_name}.gen.cpp")

    @staticmethod
    def get_json_metadata_path(output_dir: str) -> str:
        """获取JSON元数据文件路径"""
        return os.path.join(output_dir, "modules_metadata.json")

    @staticmethod
    def ensure_output_directory(output_dir: str) -> None:
        """确保输出目录存在"""
        Path(output_dir).mkdir(parents=True, exist_ok=True)

    @staticmethod
    def get_all_generated_files(output_dir: str, module_names: List[str]) -> Dict[str, List[str]]:
        """获取所有生成文件的路径（重构后的版本）"""
        # 标准化路径分隔符
        def normalize_path(path: str) -> str:
            return path.replace('\\', '/')

        return {
            'global': [normalize_path(PathManager.get_generated_path(output_dir))],
            'modules': [normalize_path(PathManager.get_companion_file_path(output_dir, name)) for name in module_names],
            'metadata': [normalize_path(PathManager.get_json_metadata_path(output_dir))]
        }


def setup_logging(verbose: bool = False) -> None:
    """设置日志"""
    level = logging.DEBUG if verbose else logging.INFO
    format_str = "%(asctime)s - %(levelname)s - %(message)s"

    logging.basicConfig(
        level=level,
        format=format_str,
        datefmt="%Y-%m-%d %H:%M:%S"
    )


def get_common_template_variables() -> Dict[str, Any]:
    """获取通用模板变量"""
    return {
        'timestamp': datetime.now().isoformat(),
        'generation_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'generator_version': '3.0.0'
    }
