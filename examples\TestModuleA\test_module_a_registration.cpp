﻿/**
 * @file test_module_a_registration.cpp
 * @brief TestModuleA的模块注册实现 - 条件4：显式注册
 *
 * 这个文件实现了README.md中条件4的要求：
 * "主模块类需要在编译期或运行时显式注册(通过modular.registry)"
 *
 * 使用REGISTER_MODULE宏系统进行模块注册，支持完整的元数据定义
 * 展示模块依赖关系：TestModuleA -> TestModule
 */

// 导入必要的模块
import modular.concepts;  // 获取概念约束和注册函数
import TestModuleA;       // 导入主模块文件（符合条件2的命名规范）

// 包含宏定义
#include "../../includes/modular_macros.h"

// 条件4：使用REGISTER_MODULE宏注册模块元数据
// 这确保了TestModuleA类符合Module概念约束并在框架中正确注册
// 优先级设置为60，低于TestModule(75)，确保正确的初始化顺序
REGISTER_MODULE(TestModuleA, "TestModuleA", "1.1.0", "依赖TestModule的示例模块，展示模块间依赖关系", 60)

/**
 * @brief 模块注册验证
 *
 * 这个注册确保：
 * 1. TestModuleA类符合modular.concepts concept Module约束
 * 2. 模块元数据正确注册到modular.registry
 * 3. 依赖关系被正确识别和处理
 * 4. 初始化优先级低于依赖的TestModule
 *
 * 注册信息：
 * - 名称: "TestModuleA"
 * - 版本: "1.0.0"
 * - 描述: "依赖TestModule的示例模块，展示模块间依赖关系"
 * - 优先级: 60 (低于TestModule的75，确保TestModule先初始化)
 * - 依赖: TestModule
 */
