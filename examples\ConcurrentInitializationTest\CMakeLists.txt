# ConcurrentInitializationTest CMakeLists.txt
# 并发初始化测试程序

# 创建可执行文件
add_executable(ConcurrentInitializationTest)

# 添加源文件
target_sources(ConcurrentInitializationTest
    PRIVATE
        concurrent_initialization_test.cpp
)

# 添加策略模块
target_sources(ConcurrentInitializationTest
    PRIVATE
        FILE_SET CXX_MODULES FILES
            concurrent_strategy.ixx
)

# 移除简化测试程序，使用真实的ModuleManager测试

# 添加生成的模块文件到测试项目
# 使用include_directories而不是FILE_SET，因为生成的文件在构建目录中
target_include_directories(ConcurrentInitializationTest
    PRIVATE
        ${CMAKE_BINARY_DIR}/modula_generated
)

# 注意：移除生成文件的重复添加，避免模块名称冲突
# 生成的元数据文件已经通过 modula 库提供

# 链接到modula库和所有测试模块
target_link_libraries(ConcurrentInitializationTest
    PRIVATE
        modula
        CoreModule
        DatabaseModule
        NetworkModule
        CacheModule
        ServiceModule
        LoggingModule
        UIModule
)

# 移除简化测试程序的链接配置

# 设置C++标准
set_target_properties(ConcurrentInitializationTest PROPERTIES
    CXX_STANDARD 23
    CXX_STANDARD_REQUIRED ON
    CXX_EXTENSIONS OFF
)

# 移除简化测试程序的属性设置

# ============================================================================
# Enhanced Encoding and Compiler Configuration
# ============================================================================

if(CMAKE_CXX_COMPILER_ID STREQUAL "MSVC")
    target_compile_options(ConcurrentInitializationTest PRIVATE
        /W4                     # High warning level
        /permissive-           # Strict conformance mode
        /utf-8                 # UTF-8 source and execution character sets
        /std:c++latest        # Latest C++ standard
        /experimental:module   # Enable C++ modules
        /bigobj               # Support large object files
        /Zc:__cplusplus       # Correct __cplusplus macro value
        /wd4819               # Disable specific encoding warning C4819
    )
    target_compile_definitions(ConcurrentInitializationTest PRIVATE
        UNICODE                  # Enable Unicode support
        _UNICODE                 # Enable Unicode support
        _CRT_SECURE_NO_WARNINGS # Disable CRT security warnings
        NOMINMAX                # Prevent min/max macro conflicts
    )
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
    target_compile_options(ConcurrentInitializationTest PRIVATE
        -Wall                    # Enable all warnings
        -Wextra                  # Enable extra warnings
        -Wpedantic              # Enable pedantic warnings
        -std=c++23              # C++23 standard
        -fdiagnostics-color=always # Colored diagnostics
        -fmodules-ts            # Enable C++ modules
        -finput-charset=UTF-8   # Input character set
        -fexec-charset=UTF-8    # Execution character set
        -fwide-exec-charset=UTF-32LE # Wide character set
    )
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
    target_compile_options(ConcurrentInitializationTest PRIVATE
        -Wall                    # Enable all warnings
        -Wextra                  # Enable extra warnings
        -Wpedantic              # Enable pedantic warnings
        -std=c++23              # C++23 standard
        -fdiagnostics-color=always # Colored diagnostics
        -fmodules               # Enable C++ modules
        -finput-charset=UTF-8   # Input character set
        -fexec-charset=UTF-8    # Execution character set
    )
endif()

# 添加编译定义
target_compile_definitions(ConcurrentInitializationTest PRIVATE
    MODULA_CXX_STANDARD=23
    MODULA_MODULES_AVAILABLE=1
    CONCURRENT_TEST_VERSION_MAJOR=1
    CONCURRENT_TEST_VERSION_MINOR=0
    CONCURRENT_TEST_VERSION_PATCH=0
)

# 设置输出目录
set_target_properties(ConcurrentInitializationTest PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
    OUTPUT_NAME "concurrent_test"
)

# 可选：添加安装配置
if(MODULA_INSTALL_TEST_MODULES)
    install(TARGETS ConcurrentInitializationTest
        RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
    )
endif()
