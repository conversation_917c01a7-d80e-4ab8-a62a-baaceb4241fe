# Examples CMakeLists.txt
# ModulaFramework v1.0 - 综合模块测试示例
# 展示完整的模块依赖关系和生命周期管理

# ============================================================================
# 传统C++库 - 非模块的静态库
# ============================================================================
add_subdirectory(TestLib)        # 传统C++库，提供实用功能

# ============================================================================
# 核心测试模块 - 基础功能验证
# ============================================================================
add_subdirectory(TestModule)     # 基础模块，无依赖
add_subdirectory(TestModuleA)    # 依赖TestModule和TestLib
add_subdirectory(TestModuleB)    # 依赖TestModule、TestModuleA和TestLib

# ============================================================================
# 扩展示例模块 - 展示不同场景的模块实现
# ============================================================================
add_subdirectory(CoreModule)     # 核心服务模块，无依赖
add_subdirectory(LoggingModule)  # 日志服务模块，依赖CoreModule
add_subdirectory(DatabaseModule) # 数据库服务模块，依赖CoreModule和LoggingModule
add_subdirectory(NetworkModule)  # 网络服务模块，依赖CoreModule和LoggingModule
add_subdirectory(CacheModule)    # 缓存服务模块，依赖CoreModule和DatabaseModule
add_subdirectory(ServiceModule)  # 业务服务模块，依赖多个基础模块
add_subdirectory(UIModule)       # 用户界面模块，依赖ServiceModule

# ============================================================================
# 综合模块验证程序
# ============================================================================
add_executable(comprehensive_module_verification comprehensive_module_verification.cpp)

message(STATUS "comprehensive_module_verification")

# 链接到modula库、传统库和所有模块
target_link_libraries(comprehensive_module_verification PRIVATE
    modula
    TestLib
    # 核心测试模块
    TestModule
    TestModuleA
    TestModuleB
    # 扩展示例模块
    CoreModule
    LoggingModule
    DatabaseModule
    NetworkModule
    CacheModule
    ServiceModule
    UIModule
)

# 设置C++标准
set_target_properties(comprehensive_module_verification PROPERTIES
    CXX_STANDARD 20
    CXX_STANDARD_REQUIRED ON
    CXX_EXTENSIONS OFF
)

# ============================================================================
# Enhanced Encoding and Compiler Configuration for Examples
# ============================================================================

# Function to apply enhanced encoding settings to a target
function(apply_enhanced_encoding_settings target_name)
    if(CMAKE_CXX_COMPILER_ID STREQUAL "MSVC")
        target_compile_options(${target_name} PRIVATE
            /W4                     # High warning level
            /permissive-           # Strict conformance mode
            /utf-8                 # UTF-8 source and execution character sets
            /std:c++latest        # Latest C++ standard
            /experimental:module   # Enable C++ modules
            /bigobj               # Support large object files
            /Zc:__cplusplus       # Correct __cplusplus macro value
            /wd4819               # Disable specific encoding warning
        )
        target_compile_definitions(${target_name} PRIVATE
            UNICODE                  # Enable Unicode support
            _UNICODE                 # Enable Unicode support
            _CRT_SECURE_NO_WARNINGS # Disable CRT security warnings
            NOMINMAX                # Prevent min/max macro conflicts
        )
    elseif(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
        target_compile_options(${target_name} PRIVATE
            -Wall                    # Enable all warnings
            -Wextra                  # Enable extra warnings
            -Wpedantic              # Enable pedantic warnings
            -std=c++20              # C++20 standard
            -fdiagnostics-color=always # Colored diagnostics
            -fmodules-ts            # Enable C++ modules
            -finput-charset=UTF-8   # Input character set
            -fexec-charset=UTF-8    # Execution character set
            -fwide-exec-charset=UTF-32LE # Wide character set
        )
    elseif(CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
        target_compile_options(${target_name} PRIVATE
            -Wall                    # Enable all warnings
            -Wextra                  # Enable extra warnings
            -Wpedantic              # Enable pedantic warnings
            -std=c++20              # C++20 standard
            -fdiagnostics-color=always # Colored diagnostics
            -fmodules               # Enable C++ modules
            -finput-charset=UTF-8   # Input character set
            -fexec-charset=UTF-8    # Execution character set
        )
    endif()
endfunction()

# Apply enhanced encoding settings to all example targets
apply_enhanced_encoding_settings(comprehensive_module_verification)

# 添加编译定义
target_compile_definitions(comprehensive_module_verification PRIVATE
    MODULA_CXX_STANDARD=20
    MODULA_MODULES_AVAILABLE=1
)

# 设置模块依赖关系
# 确保所有模块在验证程序之前构建
add_dependencies(comprehensive_module_verification
    # 核心测试模块
    TestModule TestModuleA TestModuleB
    # 扩展示例模块
    CoreModule LoggingModule DatabaseModule NetworkModule
    CacheModule ServiceModule UIModule
)

# ============================================================================
# 配置说明和验证注释
# ============================================================================
# 此配置展示了ModulaFramework v1.0的完整功能：
#
# 核心测试模块：
# 1. TestModule (基础模块，无依赖)
# 2. TestModuleA (依赖TestModule和TestLib)
# 3. TestModuleB (依赖TestModule、TestModuleA和TestLib)
#
# 扩展示例模块：
# 4. CoreModule (核心服务模块，无依赖)
# 5. LoggingModule (日志服务，依赖CoreModule)
# 6. DatabaseModule (数据库服务，依赖CoreModule和LoggingModule)
# 7. NetworkModule (网络服务，依赖CoreModule和LoggingModule)
# 8. CacheModule (缓存服务，依赖CoreModule和DatabaseModule)
# 9. ServiceModule (业务服务，依赖多个基础模块)
# 10. UIModule (用户界面，依赖ServiceModule)
#
# 特性展示：
# - 复杂的模块依赖关系和初始化顺序管理
# - C++20/23模块系统和传统头文件的混合使用
# - 自动元数据生成和注册
# - 并发初始化性能测试
# - 完整的模块生命周期管理
# - 支持MSVC和GCC编译器
