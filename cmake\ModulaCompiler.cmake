# ==============================================================================
# ModulaCompiler.cmake - Compiler Configuration
# ==============================================================================
#
# Provides comprehensive compiler configuration for the Modula Framework.
# Handles C++ standard setup, modules support, and compiler-specific optimizations.

cmake_minimum_required(VERSION 3.28)
include_guard(GLOBAL)

# ==============================================================================
# C++ Standard Configuration
# ==============================================================================

#[=======================================================================[.rst:
.. command:: modula_setup_cxx_standard

  Configure C++ standard settings.

  .. code-block:: cmake

    modula_setup_cxx_standard([STANDARD <version>])

  ``STANDARD``
    C++ standard version (default: 20, minimum: 20)

  Sets up global C++ standard requirements and validates compiler support.
#]=======================================================================]
function(modula_setup_cxx_standard)
    set(options "")
    set(one_value_args STANDARD)
    set(multi_value_args "")
    cmake_parse_arguments(SETUP "${options}" "${one_value_args}" "${multi_value_args}" ${ARGN})

    if(NOT SETUP_STANDARD)
        set(SETUP_STANDARD 20)
    endif()

    if(SETUP_STANDARD LESS 20)
        message(FATAL_ERROR
            "Modula Framework requires C++20 or later. "
            "Requested: C++${SETUP_STANDARD}")
    endif()

    # Configure global C++ standard settings
    set(CMAKE_CXX_STANDARD ${SETUP_STANDARD} PARENT_SCOPE)
    set(CMAKE_CXX_STANDARD_REQUIRED ON PARENT_SCOPE)
    set(CMAKE_CXX_EXTENSIONS OFF PARENT_SCOPE)

    message(STATUS "Modula: Configured for C++${SETUP_STANDARD} standard")
endfunction()

# ==============================================================================
# C++ Modules Configuration
# ==============================================================================

#[=======================================================================[.rst:
.. command:: modula_setup_cxx_modules

  Configure C++ modules support.

  Enables C++ modules support and configures module output directories.
#]=======================================================================]
function(modula_setup_cxx_modules)
    # Enable C++ modules support
    set(CMAKE_EXPERIMENTAL_CXX_MODULE_CMAKE_API "2182bf5c-ef0d-489a-91da-49dbc3090d2a" PARENT_SCOPE)
    set(CMAKE_EXPERIMENTAL_CXX_MODULE_DYNDEP 1 PARENT_SCOPE)

    # Configure module output directory
    set(CMAKE_CXX_MODULE_DIRECTORY "${CMAKE_BINARY_DIR}/modules" PARENT_SCOPE)
    set(CMAKE_CXX_SCAN_FOR_MODULES ON PARENT_SCOPE)

    # Enable compilation database for IDE support
    set(CMAKE_EXPORT_COMPILE_COMMANDS ON PARENT_SCOPE)

    message(STATUS "Modula: C++ modules support enabled")
endfunction()

# ==============================================================================
# Compiler-Specific Target Configuration
# ==============================================================================

#[=======================================================================[.rst:
.. command:: modula_configure_target_compiler

  Apply compiler-specific configuration to a target.

  .. code-block:: cmake

    modula_configure_target_compiler(<target>)

  ``target``
    Target name to configure

  Applies essential compiler configuration including warnings,
  optimizations, and C++ modules support.
#]=======================================================================]
function(modula_configure_target_compiler target)
    if(NOT TARGET ${target})
        message(FATAL_ERROR
            "modula_configure_target_compiler: Target '${target}' does not exist")
    endif()

    # Set C++ standard requirement for target
    target_compile_features(${target} PUBLIC cxx_std_20)

    # Compiler-specific configuration using generator expressions
    target_compile_options(${target} PRIVATE
        # MSVC configuration
        $<$<CXX_COMPILER_ID:MSVC>:
            /std:c++latest          # Use the latest C++ standard
            /W4                     # High warning level
            /permissive-            # Strict conformance mode
            /utf-8                  # UTF-8 source and execution character sets
            /experimental:module    # Enable C++ modules
            /bigobj                 # Support large object files
            $<$<CONFIG:Debug>:/Od /Zi>           # Debug optimization
            $<$<CONFIG:Release>:/O2 /DNDEBUG>    # Release optimization
        >

        # GCC configuration
        $<$<CXX_COMPILER_ID:GNU>:
            -Wall                    # Enable all warnings
            -Wextra                  # Enable extra warnings
            -fmodules-ts             # Enable C++ modules
            $<$<CONFIG:Debug>:-g -O0>            # Debug optimization
            $<$<CONFIG:Release>:-O3 -DNDEBUG>    # Release optimization
        >

        # Clang configuration
        $<$<CXX_COMPILER_ID:Clang>:
            -Wall                    # Enable all warnings
            -Wextra                  # Enable extra warnings
            -fmodules                # Enable C++ modules
            $<$<CONFIG:Debug>:-g -O0>            # Debug optimization
            $<$<CONFIG:Release>:-O3 -DNDEBUG>    # Release optimization
        >
    )

    # Compiler-specific definitions
    target_compile_definitions(${target} PRIVATE
        # MSVC-specific definitions
        $<$<CXX_COMPILER_ID:MSVC>:
            _CRT_SECURE_NO_WARNINGS  # Disable CRT security warnings
            NOMINMAX                 # Prevent min/max macro conflicts
            WIN32_LEAN_AND_MEAN     # Reduce Windows header bloat
        >

        # Build configuration definitions
        $<$<CONFIG:Debug>:MODULA_DEBUG_BUILD=1>
        $<$<CONFIG:Release>:MODULA_RELEASE_BUILD=1>

        # C++ standard feature detection
        $<$<COMPILE_FEATURES:cxx_std_20>:MODULA_CXX20_AVAILABLE=1>
        $<$<COMPILE_FEATURES:cxx_std_23>:MODULA_CXX23_AVAILABLE=1>
    )

    message(STATUS "Modula: Configured compiler settings for target '${target}'")
endfunction()

# ==============================================================================
# Initialization
# ==============================================================================

# Initialize compiler configuration
modula_setup_cxx_standard()
modula_setup_cxx_modules()

message(STATUS "Modula: Compiler configuration system loaded")
