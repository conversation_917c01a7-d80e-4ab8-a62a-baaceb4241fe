/**
 * @file modula.registry.ixx
 * @brief Module registration system
 *
 * Provides module registration and query functionality.
 *
 * @version 1.0.0
 */

module;

#include <functional>
#include <memory>
#include <shared_mutex>
#include <typeindex>
#include <unordered_map>
#include <vector>
#include <algorithm>
#include <iostream>
#include <optional>
#include <map>
#include <string>

export module modula.registry;

import modula.metadata;
import modula.types;
import modula.info;

export namespace modula {
/**
 * @brief Module registration information
 *
 * Contains module metadata and lifecycle management functions.
 * Provides type-safe module operation interface with factory pattern creation,
 * initialization, shutdown and state query capabilities.
 */
struct ModuleRegistration
{
    std::function<ModuleInstanceHolder()> factory; ///< Module factory function for creating instances
    std::function<bool(void*)> initializer;        ///< Module initializer, returns initialization result
    std::function<void(void*)> shutdown;           ///< Module shutdown handler for cleanup operations
    std::function<bool(void*)> state_query;        ///< Module state query for checking initialization status
    ModuleInfo module_info;                        ///< 完整的模块信息对象

    /**
     * @brief Default constructor
     */
    ModuleRegistration() = default;

    /**
     * @brief Move constructor
     */
    ModuleRegistration(ModuleRegistration&&) = default;

    /**
     * @brief Move assignment operator
     */
    ModuleRegistration& operator=(ModuleRegistration&&) = default;

    // Disable copy operations for performance
    ModuleRegistration(const ModuleRegistration&) = delete;
    ModuleRegistration& operator=(const ModuleRegistration&) = delete;

    /**
     * @brief Validate registration information completeness
     * @return True if all required functions are set
     */
    [[nodiscard]] bool is_valid() const noexcept {
        return factory && initializer && shutdown && state_query;
    }
};

/**
 * @brief Module group for parallel execution
 *
 * A container class that groups modules based on their parallel_group metadata.
 * Modules within the same group have dependencies and must be executed sequentially,
 * while different groups can be executed in parallel.
 */
class ModuleGroup
{
public:
    /**
     * @brief Default constructor
     */
    ModuleGroup() : group_id_(0) {}

    /**
     * @brief Constructor with group ID
     * @param group_id The parallel group identifier
     */
    explicit ModuleGroup(int group_id) : group_id_(group_id) {}

    /**
     * @brief Add a module registration to this group
     * @param type_idx Type index of the module
     * @param registration Pointer to the module registration
     */
    void add_module(const std::type_index& type_idx, const struct ModuleRegistration* registration) {
        if (registration) {
            modules_.emplace_back(type_idx, registration);
        }
    }

    /**
     * @brief Get the group ID
     * @return The parallel group identifier
     */
    [[nodiscard]] int get_group_id() const noexcept {
        return group_id_;
    }

    /**
     * @brief Get all modules in this group
     * @return Vector of (type_index, registration) pairs
     */
    [[nodiscard]] const std::vector<std::pair<std::type_index, const struct ModuleRegistration*>>& get_modules() const noexcept {
        return modules_;
    }

    /**
     * @brief Get the number of modules in this group
     * @return Number of modules
     */
    [[nodiscard]] std::size_t size() const noexcept {
        return modules_.size();
    }

    /**
     * @brief Check if the group is empty
     * @return True if no modules in the group
     */
    [[nodiscard]] bool empty() const noexcept {
        return modules_.empty();
    }

    /**
     * @brief Sort modules by priority (higher priority first)
     */
    void sort_by_priority() {
        std::sort(modules_.begin(), modules_.end(),
                 [](const auto& a, const auto& b) {
            return a.second->module_info.parallel_priority() > b.second->module_info.parallel_priority();
                 });
    }

private:
    int group_id_;                                                                    ///< Parallel group identifier
    std::vector<std::pair<std::type_index, const struct ModuleRegistration*>> modules_;    ///< Modules in this group
};


/**
 * @brief Module registry singleton
 *
 * Thread-safe module registration and query system.
 */
class ModuleRegistry
{
public:
    /**
     * @brief Get singleton instance
     * @return Reference to the singleton instance
     */
    static ModuleRegistry& instance() {
        static ModuleRegistry instance;
        return instance;
    }

    /**
     * @brief Register module
     * @tparam T Module type, must satisfy Module concept
     * @return True if registration succeeds, false if module already exists
     */
    template<Module T>
    bool register_module() {
        std::lock_guard lock(mutex_);
        auto type_idx = std::type_index(typeid(T));

        if (registrations_.contains(type_idx)) {
            return false; // 模块已注册
        }

        ModuleRegistration registration;

        // 使用统一的create_module_info函数获取完整的模块信息
        registration.module_info = create_module_info<T>();

        // Set factory function
        registration.factory = []() -> ModuleInstanceHolder {
            return ModuleInstanceHolder(std::make_unique<T>());
        };

        // Set initialization function
        registration.initializer = [](void* ptr) -> bool {
            if (!ptr) [[unlikely]] {
                return false;
            }
            return static_cast<T*>(ptr)->initialize();
        };

        // Set shutdown function
        registration.shutdown = [](void* ptr) noexcept {
            if (!ptr) [[unlikely]] {
                return;
            }
            try {
                static_cast<T*>(ptr)->shutdown();
            } catch (...) {
                // Silently handle shutdown exceptions to prevent termination
            }
        };

        // Set state query function
        registration.state_query = [](void* ptr) noexcept -> bool {
            if (!ptr) [[unlikely]] {
                return false;
            }
            try {
                return static_cast<T*>(ptr)->is_initialized();
            } catch (...) {
                return false; // Assume not initialized on exception
            }
        };

        // Store registration
        registrations_.emplace(type_idx, std::move(registration));

        // 记录注册顺序
        registration_order_.push_back(type_idx);

        return true;
    }

    /**
     * @brief Check if module is registered
     * @tparam T Module type, must satisfy Module concept
     * @return True if module is registered
     */
    template<Module T>
    [[nodiscard]] bool is_registered() const {
        std::shared_lock lock(mutex_);
        auto type_key = std::type_index(typeid(T));
        return registrations_.contains(type_key);
    }

    /**
     * @brief Get module registration information by type
     * @tparam T Module type, must satisfy Module concept
     * @return Registration information pointer, nullptr if not found
     */
    template<Module T>
    [[nodiscard]] const ModuleRegistration* get_registration() const {
        std::shared_lock lock(mutex_);
        auto type_key = std::type_index(typeid(T));
        auto it = registrations_.find(type_key);
        return (it != registrations_.end()) ? &it->second : nullptr;
    }

    /**
     * @brief Get module registration by type index
     * @param type_idx Type index of the module
     * @return Pointer to registration if found, nullptr otherwise
     */
    [[nodiscard]] const struct ModuleRegistration* get_registration_by_type_index(const std::type_index& type_idx) const {
        std::shared_lock lock(mutex_);
        auto it = registrations_.find(type_idx);
        return (it != registrations_.end()) ? &it->second : nullptr;
    }

    /**
     * @brief Unregister module by type
     * @tparam T Module type, must satisfy Module concept
     * @return True if successfully unregistered, false if module does not exist
     */
    template<Module T>
    bool unregister_module() {
        std::lock_guard lock(mutex_);
        auto type_key = std::type_index(typeid(T));

        // 从注册表中移除
        bool erased = registrations_.erase(type_key) > 0;

        if (erased) {
            // 从注册顺序中移除
            auto it = std::find(registration_order_.begin(), registration_order_.end(), type_key);
            if (it != registration_order_.end()) {
                registration_order_.erase(it);
            }
        }

        return erased;
    }

    /**
     * @brief Get all registrations in registration order
     * @return Vector of type_index and registration pairs
     */
    [[nodiscard]] std::vector<std::pair<std::type_index, const ModuleRegistration*>>
    get_all_registrations() const {
        std::shared_lock lock(mutex_);
        std::vector<std::pair<std::type_index, const ModuleRegistration*>> result;
        result.reserve(registration_order_.size());

        for (const auto& type_idx : registration_order_) {
            auto it = registrations_.find(type_idx);
            if (it != registrations_.end()) {
                result.emplace_back(type_idx, &it->second);
            }
        }

        return result;
    }

    /**
     * @brief Get total number of registered modules
     * @return Number of registered modules
     */
    [[nodiscard]] std::size_t size() const noexcept {
        std::shared_lock lock(mutex_);
        return registrations_.size();
    }

    /**
     * @brief Check if registry is empty
     * @return True if no modules are registered
     */
    [[nodiscard]] bool empty() const noexcept {
        std::shared_lock lock(mutex_);
        return registrations_.empty();
    }

    /**
     * @brief Batch register modules
     *
     * Batch registration of multiple module types using fold expressions.
     * Provides transactional semantics: if any module registration fails,
     * the entire operation is considered failed.
     *
     * @tparam Modules List of module types to register, all must satisfy Module concept
     * @return Returns true if all modules are registered successfully.
     */
    template<Module... Modules>
    bool register_modules() {
        bool all_success = true;
        try {
            ((all_success &= register_module<Modules>()), ...);
        } catch (const std::exception&) {
            all_success = false;
        }
        return all_success;
    }

    /**
     * @brief Clear all registrations
     */
    void clear() noexcept {
        std::lock_guard lock(mutex_);
        registrations_.clear();
        registration_order_.clear();
    }

    /**
     * @brief Get modules grouped by parallel group
     * @return Map of parallel group ID to modules in that group
     */
    std::map<int, std::vector<std::pair<std::type_index, const ModuleRegistration*>>> get_modules_by_parallel_group() const {
        std::shared_lock lock(mutex_);
        std::map<int, std::vector<std::pair<std::type_index, const ModuleRegistration*>>> groups;

        for (const auto& [type_idx, registration] : registrations_) {
            int group_id = registration.module_info.parallel_group();
            groups[group_id].emplace_back(type_idx, &registration);
        }

        // 按优先级排序每个组内的模块
        for (auto& [group_id, modules] : groups) {
            std::sort(modules.begin(), modules.end(),
                     [](const auto& a, const auto& b) {
                return a.second->module_info.parallel_priority() > b.second->module_info.parallel_priority();
                     });
        }

        return groups;
    }

    /**
     * @brief Create module groups for parallel execution
     * @return Vector of ModuleGroup objects sorted by group ID
     */
    std::vector<ModuleGroup> create_module_groups() const {
        std::shared_lock lock(mutex_);
        std::map<int, ModuleGroup> group_map;

        // Create groups and add modules
        for (const auto& [type_idx, registration] : registrations_) {
            int group_id = registration.module_info.parallel_group();

            // Create group if it doesn't exist
            if (group_map.find(group_id) == group_map.end()) {
                group_map.emplace(group_id, ModuleGroup(group_id));
            }

            // Add module to the group
            group_map[group_id].add_module(type_idx, &registration);
        }

        // Sort modules within each group by priority and convert to vector
        std::vector<ModuleGroup> groups;
        groups.reserve(group_map.size());

        for (auto& [group_id, group] : group_map) {
            group.sort_by_priority();
            groups.emplace_back(std::move(group));
        }

        // Sort groups by group ID for deterministic execution order
        std::sort(groups.begin(), groups.end(),
                 [](const ModuleGroup& a, const ModuleGroup& b) {
                     return a.get_group_id() < b.get_group_id();
                 });

        return groups;
    }

private:
    ModuleRegistry() = default;

    mutable std::shared_mutex mutex_;
    std::unordered_map<std::type_index, ModuleRegistration> registrations_;
    std::vector<std::type_index> registration_order_;
};

/**
 * @brief Register module
 * @tparam T Module type, must satisfy Module concept
 * @return True if registration succeeds, false if module already exists
 */
template<Module T>
bool register_module() {
    return ModuleRegistry::instance().register_module<T>();
}

/**
 * @brief Register module with user data
 * @tparam T Module type, must satisfy Module concept
 * @param user_data User-defined module data
 * @return True if registration succeeds, false if module already exists
 */
template<Module T>
bool register_module(const UserData& user_data) {
    if (ModuleRegistry::instance().is_registered<T>()) {
        return false;
    }

    if (!UserDataRegistry<T>::register_data(user_data)) {
        return false;
    }

    return ModuleRegistry::instance().register_module<T>();
}

/**
 * @brief 检查模块是否已注册（便捷函数）
 * @tparam T 模块类型，必须满足Module概念
 * @return 如果模块已注册返回true
 */
template<Module T>
[[nodiscard]] bool is_module_registered() {
    return ModuleRegistry::instance().is_registered<T>();
}

/**
 * @brief 获取模块注册信息（便捷函数）
 * @tparam T 模块类型，必须满足Module概念
 * @return 注册信息指针，如果未找到返回nullptr
 */
template<Module T>
[[nodiscard]] const ModuleRegistration* get_module_registration() {
    return ModuleRegistry::instance().get_registration<T>();
}

/**
 * @brief 注销模块（便捷函数）
 * @tparam T 模块类型，必须满足Module概念
 * @return 如果成功注销返回true
 */
template<Module T>
bool unregister_module() {
    return ModuleRegistry::instance().unregister_module<T>();
}

} // namespace modula
