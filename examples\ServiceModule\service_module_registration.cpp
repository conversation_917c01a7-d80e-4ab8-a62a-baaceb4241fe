/**
 * @file service_module_registration.cpp
 * @brief ServiceModule注册实现
 */

#include <iostream>
import modula.registry;
import ServiceModule;

namespace {
    struct ServiceModuleRegistration {
        ServiceModuleRegistration() {
            try {
                std::cout << "[ServiceModule] 正在注册到模块系统..." << std::endl;
                // 暂时注释掉直接注册调用，改为通过生成的代码自动注册
                // bool success = modula::register_module<ServiceModule>();
                std::cout << "[ServiceModule] 将通过自动注册器完成注册" << std::endl;
                // if (success) {
                //     std::cout << "[ServiceModule] 注册完成" << std::endl;
                // } else {
                //     std::cerr << "[ServiceModule] 注册失败 - 模块可能已存在" << std::endl;
                // }
            } catch (const std::exception& e) {
                std::cerr << "[ServiceModule] 注册失败: " << e.what() << std::endl;
            }
        }
    };
    static ServiceModuleRegistration service_module_registration;
}
