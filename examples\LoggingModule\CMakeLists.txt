# LoggingModule CMakeLists.txt
add_library(LoggingModule)
set_target_properties(LoggingModule PROPERTIES VERSION 1.0.0)

target_sources(LoggingModule
    PUBLIC FILE_SET CXX_MODULES FILES LoggingModule.module.ixx
    PRIVATE logging_module_registration.cpp
)

target_link_libraries(LoggingModule PUBLIC modula CacheModule)
declare_module(LoggingModule)

set_target_properties(LoggingModule PROPERTIES
    CXX_STANDARD 23 CXX_STANDARD_REQUIRED ON CXX_EXTENSIONS OFF
)

if(CMAKE_CXX_COMPILER_ID STREQUAL "MSVC")
    target_compile_options(LoggingModule PRIVATE /W4 /permissive- /utf-8 /std:c++latest /experimental:module)
    target_compile_definitions(LoggingModule PRIVATE UNICODE _UNICODE _CRT_SECURE_NO_WARNINGS NOMINMAX)
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
    target_compile_options(LoggingModule PRIVATE -Wall -Wextra -std=c++23 -fmodules-ts)
endif()

target_compile_definitions(LoggingModule PRIVATE MODULA_CXX_STANDARD=23 MODULA_MODULES_AVAILABLE=1)
