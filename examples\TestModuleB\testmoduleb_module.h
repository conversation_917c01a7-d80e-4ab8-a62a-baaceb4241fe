/**
 * @file testmoduleb_module.h
 * @brief TestModuleB - 依赖TestModule和TestModuleA的复杂示例模块（传统头文件版本）
 *
 * 这是一个符合README.md规范的示例模块，展示复杂的模块间依赖关系：
 * 条件1：通过enable_modula_framework()在CMake中声明
 * 条件2：主模块文件命名为<目录名>_module.h，使用传统头文件格式
 * 条件3：实现符合modula.concepts concept Module的生命周期管理类
 * 条件4：通过modula.registry显式注册
 *
 * 依赖关系：TestModuleB -> TestModuleA -> TestModule
 *
 * @version 1.0.0
 * <AUTHOR> Team
 */

#pragma once

#include <iostream>
#include <string>
#include <chrono>
#include <vector>
#include <memory>

// 包含传统C++库头文件
#include "test_lib.h"

// 包含依赖的模块头文件
// 注意：这里假设TestModule和TestModuleA也有对应的头文件版本
// 如果它们仍然是模块，则需要通过import语句导入

/**
 * @brief TestModuleB类 - 符合Module概念约束的生命周期管理类
 *
 * 这是主模块类，实现了README.md中条件3要求的概念约束：
 * - 符合modula.concepts concept Module的接口要求
 * - 实现完整的生命周期管理：initialize(), shutdown(), is_initialized()
 * - 依赖TestModule和TestModuleA模块，展示复杂的模块间依赖关系
 * - 通过REGISTER_MODULE宏进行注册（条件4）
 *
 * 使用方式：
 * ```cpp
 * #include "testmoduleb_module.h"
 * import modula;
 *
 * auto manager = modula::initialize_framework();
 * auto* test_module_b = manager->get_module<TestModuleB>("TestModuleB");
 * ```
 */
class TestModuleB {
public:
    /**
     * @brief 默认构造函数
     */
    TestModuleB() = default;

    /**
     * @brief 析构函数
     */
    ~TestModuleB() {
        if (initialized_) {
            shutdown();
        }
    }

    /**
     * @brief 初始化模块
     *
     * 实现Module概念要求的初始化接口
     * 注意：依赖的TestModule和TestModuleA应该在此模块之前初始化
     * @return bool 初始化成功返回true，失败返回false
     */
    bool initialize() {
        if (initialized_) {
            std::cout << "[TestModuleB] Already initialized" << std::endl;
            return true;
        }

        std::cout << "[TestModuleB] Initializing..." << std::endl;

        // 模拟初始化过程
        initialization_time_ = std::chrono::steady_clock::now();

        // 验证依赖模块状态
        std::cout << "[TestModuleB] Verifying dependencies..." << std::endl;
        std::cout << "[TestModuleB] - Checking TestModule dependency..." << std::endl;
        std::cout << "[TestModuleB] - Checking TestModuleA dependency..." << std::endl;
        
        // 模拟复杂的初始化工作
        std::cout << "[TestModuleB] Loading advanced configuration..." << std::endl;
        std::cout << "[TestModuleB] Initializing complex resources..." << std::endl;
        std::cout << "[TestModuleB] Setting up inter-module communication..." << std::endl;

        // 模拟一些处理时间
        auto start = std::chrono::steady_clock::now();
        while (std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - start).count() < 100) {
            // 模拟初始化工作
        }

        initialized_ = true;
        std::cout << "[TestModuleB] Initialization completed successfully" << std::endl;
        return true;
    }

    /**
     * @brief 关闭模块
     *
     * 实现Module概念要求的关闭接口
     */
    void shutdown() {
        if (!initialized_) {
            std::cout << "[TestModuleB] Already shutdown" << std::endl;
            return;
        }

        std::cout << "[TestModuleB] Shutting down..." << std::endl;

        // 这里可以添加实际的清理逻辑
        std::cout << "[TestModuleB] Closing inter-module communication..." << std::endl;
        std::cout << "[TestModuleB] Releasing complex resources..." << std::endl;
        std::cout << "[TestModuleB] Saving advanced state..." << std::endl;

        initialized_ = false;
        std::cout << "[TestModuleB] Shutdown completed" << std::endl;
    }

    /**
     * @brief 检查模块是否已初始化
     *
     * 实现Module概念要求的状态查询接口
     * @return bool 已初始化返回true，否则返回false
     */
    bool is_initialized() const noexcept {
        return initialized_;
    }

    /**
     * @brief 获取模块版本（扩展接口）
     * @return std::string 版本号
     */
    std::string get_version() const {
        return "1.0.0";
    }

    /**
     * @brief 获取模块依赖列表（扩展接口）
     * @return std::vector<std::string> 依赖的模块名称列表
     */
    std::vector<std::string> get_dependencies() const {
        // TestModuleB依赖TestModule和TestModuleA
        return {"TestModule", "TestModuleA"};
    }

    /**
     * @brief 获取模块描述（扩展接口）
     * @return std::string 模块描述
     */
    std::string get_description() const {
        return "TestModuleB - Complex module depending on TestModule and TestModuleA";
    }

    /**
     * @brief 获取初始化时间（扩展接口）
     * @return std::chrono::steady_clock::time_point 初始化时间点
     */
    std::chrono::steady_clock::time_point get_initialization_time() const {
        return initialization_time_;
    }

    /**
     * @brief 执行复杂模块功能（示例方法）
     * @return std::string 执行结果
     */
    std::string perform_complex_task() const {
        if (!initialized_) {
            return "[TestModuleB] Error: Module not initialized";
        }
        
        return "[TestModuleB] Complex task completed successfully - demonstrating advanced module functionality with multiple dependencies";
    }

    /**
     * @brief 获取依赖模块信息（示例方法）
     * @return std::string 依赖信息
     */
    std::string get_dependency_info() const {
        if (!initialized_) {
            return "[TestModuleB] Error: Module not initialized";
        }
        
        return "[TestModuleB] Dependencies: TestModule (base), TestModuleA (intermediate) - forming a dependency chain";
    }

    /**
     * @brief 演示高级TestLib功能集成
     * @return std::string 包含高级TestLib功能演示结果的字符串
     */
    std::string demonstrate_advanced_testlib_features() const {
        if (!initialized_) {
            return "[TestModuleB] Error: Module not initialized";
        }

        std::string result = "[TestModuleB] Advanced TestLib Integration Demo:\n";

        // 演示统计功能
        result += "  Statistics analysis:\n";
        testlib::data_structures::Statistics stats;
        std::vector<double> sample_data = {1.5, 2.3, 3.7, 2.1, 4.2, 3.8, 2.9, 3.4, 2.7, 3.1};

        for (double value : sample_data) {
            stats.add_value(value);
        }

        result += "    Sample data: [1.5, 2.3, 3.7, 2.1, 4.2, 3.8, 2.9, 3.4, 2.7, 3.1]\n";
        result += "    Mean: " + std::to_string(stats.mean()) + "\n";
        result += "    Median: " + std::to_string(stats.median()) + "\n";
        result += "    Std Dev: " + std::to_string(stats.standard_deviation()) + "\n";
        result += "    Min: " + std::to_string(stats.min()) + "\n";
        result += "    Max: " + std::to_string(stats.max()) + "\n";

        // 演示复杂字符串处理
        result += "  Advanced string processing:\n";
        std::vector<std::string> words = {"Hello", "World", "from", "TestModuleB"};
        std::string joined = testlib::string_utils::join(words, " -> ");
        result += "    Joined words: " + joined + "\n";

        std::vector<std::string> split_result = testlib::string_utils::split(joined, '-');
        result += "    Split by '-': " + std::to_string(split_result.size()) + " parts\n";

        // 演示高级数学计算
        result += "  Advanced mathematical operations:\n";
        result += "    sqrt_newton(16.0) = " + std::to_string(testlib::math::sqrt_newton(16.0)) + "\n";
        result += "    power(2.5, 3) = " + std::to_string(testlib::math::power(2.5, 3)) + "\n";
        result += "    lcm(12, 18) = " + std::to_string(testlib::math::lcm(12, 18)) + "\n";

        // 演示性能基准测试
        result += "  Performance benchmarking:\n";
        testlib::performance::Timer benchmark_timer;
        benchmark_timer.start();

        // 执行一些计算密集型操作
        long long operations = 0;
        for (int i = 0; i < 10000; ++i) {
            for (int j = 0; j < 100; ++j) {
                volatile double temp = testlib::math::sqrt_newton(static_cast<double>(i + j + 1));
                operations++;
            }
        }

        double benchmark_time = benchmark_timer.stop();
        double ops_per_sec = testlib::performance::calculate_ops_per_second(operations, benchmark_time);

        result += "    Performed " + std::to_string(operations) + " sqrt operations\n";
        result += "    Time: " + testlib::performance::format_duration(benchmark_time) + "\n";
        result += "    Rate: " + std::to_string(static_cast<long long>(ops_per_sec)) + " ops/sec\n";

        return result;
    }

private:
    bool initialized_ = false;  ///< 初始化状态标志
    std::chrono::steady_clock::time_point initialization_time_;  ///< 初始化时间点
};
