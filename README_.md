# Modula - A C++ Architectural Framework

`Modula` is a modern C++ framework for architecting, analyzing, and managing complex applications. It empowers developers to build robust, scalable systems by treating software as a collection of well-defined, independent **Units**.

The core philosophy of `Modula` is to provide tools for high-level **architectural analysis** and **runtime lifecycle management**, not to interfere with the low-level compilation model.

> **A Critical Distinction:** A `Modula Unit` is a concept for **software architecture**. It represents a logical, high-level component of your application (e.g., a subsystem, a service, a major library). This concept is **entirely separate from and unrelated to** C++20's language-level `modules` (`import`/`export` syntax). `Modula` helps you manage your architecture, regardless of whether you use traditional headers or C++20 modules for compilation.

## The Modula Philosophy: Architecture as Code

`Modula` treats your application's architecture as a first-class citizen that can be declared, analyzed, and managed, both at build-time and runtime.

1.  **The Unit**: A `Unit` is the fundamental building block in a `Modula` application. It is a self-contained, replaceable component with a clearly defined public API and lifecycle. Typically, a `Unit` corresponds to a CMake target (e.g., a library).
2.  **Declarative Architecture**: You declare targets as `Units` within your CMake build system. This act registers them with the `Modula` framework.
3.  **Automated Analysis**: At CMake configure time, `Modula` inspects these `Units`, analyzes their inter-dependencies (`target_link_libraries`), and discovers their public APIs.
4.  **Metadata Generation**: This analysis results in a `units.json` metadata file—a machine-readable "blueprint" of your entire application architecture.
5.  **Intelligent Runtime**: At runtime, the `Modula` `UnitManager` uses this blueprint to:
    *   Construct a dependency graph.
    *   Detect circular dependencies.
    *   Initialize all `Units` in the correct topological order.
    *   Provide controlled shutdown in the reverse order.

## Core Features

*   **Architectural Unit Declaration**: Clearly demarcate your logical components using a simple CMake function: `modula_declare_unit()`.
*   **Automated Dependency Graphing**: Automatically builds a dependency graph from your existing `target_link_libraries` relationships. No redundant declarations needed.
*   **Build-Time Metadata Generation**: Generates a detailed JSON manifest of all `Units`, their properties, APIs, and dependencies.
*   **Robust Lifecycle Management**: A central `UnitManager` handles the automatic initialization and shutdown of all `Units` in the correct order.
*   **Non-Invasive Integration**: `Modula` analyzes your build system without forcing specific compiler flags or project structures. It works *with* your existing CMake setup.
*   **Extensible C++ Interface**: Provides clear C++ concepts (`modula::concepts::Unit`) and base classes (`modula::IUnit`) for implementing `Unit` behavior.

## The Modula Library Architecture

`Modula` is designed as a set of cohesive C++ libraries, organized by namespaces.

| Namespace              | Core Responsibility        | Key Components                               |
| :--------------------- | :------------------------- | :------------------------------------------- |
| **`modula`**           | Framework entry point      | `IUnit`, `UnitInfo`, global configuration    |
| **`modula::concepts`** | Concepts and type traits   | `concept Unit`, `unit_traits`                |
| **`modula::registry`** | Compile-time registration  | `UnitRegistry`, `MODULA_REGISTER_UNIT` macro |
| **`modula::metadata`** | Metadata structures & loading | `UnitMetadata`, `MetadataLoader` (for `units.json`) |
| **`modula::graph`**    | Dependency analysis        | Topological sort, cycle detection            |
| **`modula::manager`**  | Runtime lifecycle control  | `UnitManager`, initialization/shutdown sequences |

## A Blueprint for Your Project

### 1. CMake: Declaring a Unit

In your `CMakeLists.txt`:

```cmake
# Add your library as a standard CMake target
add_library(my_awesome_unit)

target_sources(my_awesome_unit
    PRIVATE
        src/my_awesome_unit.cpp
    PUBLIC
        # Declare the public API using a FILE_SET
        FILE_SET HEADERS
        BASE_DIRS "${CMAKE_CURRENT_SOURCE_DIR}/include"
        FILES include/my_awesome_unit/api.h
)

# Link against another unit
target_link_libraries(my_awesome_unit PUBLIC another_unit)

# Declare this target as a Modula Unit
modula_declare_unit(my_awesome_unit)
```

### 2. C++: Implementing a Unit

In your C++ code:

```cpp
// include/my_awesome_unit/api.h
#pragma once
#include <modula/IUnit.h>

class MyAwesomeUnit : public modula::IUnit {
public:
    // IUnit interface implementation
    void on_initialize() override;
    void on_shutdown() override;
};

// src/my_awesome_unit.cpp
#include "my_awesome_unit/api.h"
#include <modula/registry.h>
#include <iostream>

void MyAwesomeUnit::on_initialize() {
    std::cout << "MyAwesomeUnit Initialized!\n";
}

void MyAwesomeUnit::on_shutdown() {
    std::cout << "MyAwesomeUnit Shutdown.\n";
}

// Register this class as the implementation for the "my_awesome_unit" Unit
MODULA_REGISTER_UNIT("my_awesome_unit", MyAwesomeUnit);
```

### 3. Application Main

```cpp
#include <modula/manager.h>

int main() {
    // The UnitManager handles everything
    modula::UnitManager manager;

    // Load the architecture blueprint generated by CMake
    manager.load_metadata("path/to/units.json");

    // Initialize all registered units in the correct dependency order
    manager.initialize_all();

    // ... your application runs ...

    // Shutdown all units in reverse dependency order
    manager.shutdown_all();

    return 0;
}
```

## System Requirements

- **C++20/23 Compatible Compiler**:
  - GCC 11+
  - Clang 14+
  - MSVC 2022+ (Visual Studio 17.0+)
- **CMake 3.23+**: Required for `FILE_SET` support.
- **Operating System**: Windows 10+, Linux (Ubuntu 20.04+), macOS 12+

