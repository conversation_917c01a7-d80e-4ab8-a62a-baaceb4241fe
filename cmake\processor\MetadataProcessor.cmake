# ==============================================================================
# MetadataProcessor.cmake - Module Metadata Generation Processor
# ==============================================================================
#
# Collects detailed module metadata information and generates JSON format metadata files.
# Integrates generated metadata into the build system.

cmake_minimum_required(VERSION 3.28)
include_guard(GLOBAL)

#[=======================================================================[.rst:
MetadataProcessor - Module Metadata Generation Processor
========================================================

Core responsibilities:
- Collect detailed module metadata information
- Generate JSON format metadata files
- Integrate generated metadata into the build system

Main interface:
- modula_process_metadata(): Process metadata generation for target list

#]=======================================================================]

# ==============================================================================
# Dependencies
# ==============================================================================

include("${CMAKE_CURRENT_LIST_DIR}/../ModulaConfiguration.cmake")
include("${CMAKE_CURRENT_LIST_DIR}/../ModulaUtils.cmake")

# ==============================================================================
# Processor Registration
# ==============================================================================

if(COMMAND modula_register_processor)
    modula_register_processor("MetadataProcessor" "modula_process_metadata")
endif()

# ==============================================================================
# Configuration
# ==============================================================================

# Global constants
set(_MODULA_METADATA_DEFAULT_OUTPUT_DIR "${CMAKE_BINARY_DIR}/modula_generated")
set(_MODULA_METADATA_DEFAULT_FILENAME "modules_metadata.json")

# Configuration state
set(_MODULA_METADATA_CONFIG_LOADED FALSE CACHE INTERNAL "Metadata configuration loaded flag")

# ==============================================================================
# Main Processing Interface
# ==============================================================================

#[=======================================================================[.rst:
.. command:: modula_process_metadata

  Process metadata generation for target list (main interface).

  .. code-block:: cmake

    modula_process_metadata(<targets>)

  ``targets``
    List of targets to process

  Executes metadata generation for the specified target list:
  - Automatically loads configuration
  - Generates JSON metadata files
  - Provides detailed logging output

#]=======================================================================]
function(modula_process_metadata targets)
    if(NOT targets)
        modula_message(VERBOSE "No targets provided for metadata generation" MODULE "MetadataProcessor")
        return()
    endif()

    modula_message(VERBOSE "Processing metadata for targets: ${targets}" MODULE "MetadataProcessor")

    # Ensure configuration is loaded
    _modula_metadata_load_configuration()
    _modula_metadata_initialize_output_directory()

    # Filter valid module targets
    _modula_metadata_filter_valid_targets("${targets}" valid_targets)

    if(NOT valid_targets)
        modula_message(VERBOSE "No valid targets for metadata generation" MODULE "MetadataProcessor")
        return()
    endif()

    # Generate JSON metadata file
    get_property(output_dir CACHE _MODULA_METADATA_OUTPUT_DIR PROPERTY VALUE)
    get_property(json_filename CACHE _MODULA_METADATA_JSON_FILENAME PROPERTY VALUE)
    set(metadata_file "${output_dir}/${json_filename}")

    # Call metadata generation interface
    modula_generate_metadata("${valid_targets}" "${metadata_file}")

    if(EXISTS "${metadata_file}")
        list(LENGTH valid_targets target_count)
        modula_message(VERBOSE "Metadata processing completed successfully for ${target_count} targets" MODULE "MetadataProcessor")
    else()
        modula_message(ERROR "Metadata processing failed" MODULE "MetadataProcessor")
    endif()
endfunction()

#[=======================================================================[.rst:
.. command:: modula_generate_metadata

  Generate metadata for targets.

  .. code-block:: cmake

    modula_generate_metadata(<targets> <output_file>)

  ``targets``
    List of targets
  ``output_file``
    Output file path

  Generates complete JSON format module metadata file. This is the single
  entry point for the entire metadata processing pipeline.

#]=======================================================================]
function(modula_generate_metadata targets output_file)
    if(NOT targets)
        modula_message(STATUS "No targets specified for metadata generation" MODULE "MetadataProcessor")
        return()
    endif()

    # Filter valid module targets
    _modula_metadata_filter_valid_targets("${targets}" valid_modules)

    if(NOT valid_modules)
        modula_message(STATUS "No valid modules found for metadata generation" MODULE "MetadataProcessor")
        return()
    endif()

    # Sort targets by name for consistent output
    list(SORT valid_modules)

    # Generate timestamp
    string(TIMESTAMP generation_time "%Y-%m-%dT%H:%M:%S" UTC)

    # Calculate module statistics
    list(LENGTH valid_modules total_modules)

    # Build JSON structure
    set(json_content "{}")

    # Build metadata object
    _modula_metadata_add_header_info("${json_content}" "${generation_time}" "${total_modules}" json_content)

    # Build modules array
    string(JSON json_content SET "${json_content}" "modules" "[]")

    # Generate metadata for each module
    set(module_index 0)
    foreach(target ${valid_modules})
        _modula_metadata_collect_module_info("${target}" module_json_obj)
        if(module_json_obj)
            string(JSON json_content SET "${json_content}" "modules" "${module_index}" "${module_json_obj}")
            math(EXPR module_index "${module_index} + 1")
        endif()
    endforeach()

    # Write to file
    file(WRITE "${output_file}" "${json_content}")

    modula_message(VERBOSE "Generated metadata: ${output_file}" MODULE "MetadataProcessor")
    modula_message(VERBOSE "Processed ${total_modules} modules" MODULE "MetadataProcessor")
endfunction()

#[=======================================================================[.rst:
.. command:: _modula_metadata_load_configuration

  Load metadata configuration (internal function).

  .. code-block:: cmake

    _modula_metadata_load_configuration()

#]=======================================================================]
function(_modula_metadata_load_configuration)
    if(_MODULA_METADATA_CONFIG_LOADED)
        return()
    endif()

    # Load output directory configuration
    modula_get_config(MODULA_OUTPUT_DIR output_dir)
    if(NOT output_dir)
        set(output_dir "${_MODULA_METADATA_DEFAULT_OUTPUT_DIR}")
    endif()
    set(_MODULA_METADATA_OUTPUT_DIR "${output_dir}" CACHE INTERNAL "Metadata output directory")

    # Load JSON filename configuration
    modula_get_config(MODULA_METADATA_JSON_FILENAME json_filename)
    if(NOT json_filename)
        set(json_filename "${_MODULA_METADATA_DEFAULT_FILENAME}")
    endif()
    set(_MODULA_METADATA_JSON_FILENAME "${json_filename}" CACHE INTERNAL "JSON metadata filename")

    set(_MODULA_METADATA_CONFIG_LOADED TRUE CACHE INTERNAL "Metadata configuration loaded flag" FORCE)
    modula_message(VERBOSE "Metadata configuration loaded" MODULE "MetadataProcessor")
endfunction()

#[=======================================================================[.rst:
.. command:: _modula_metadata_initialize_output_directory

  Initialize output directory (internal function).

  .. code-block:: cmake

    _modula_metadata_initialize_output_directory()

#]=======================================================================]
function(_modula_metadata_initialize_output_directory)
    get_property(output_dir CACHE _MODULA_METADATA_OUTPUT_DIR PROPERTY VALUE)

    # Create output directory if it doesn't exist
    if(NOT EXISTS "${output_dir}")
        file(MAKE_DIRECTORY "${output_dir}")
        modula_message(VERBOSE "Created metadata output directory: ${output_dir}" MODULE "MetadataProcessor")
    endif()
endfunction()

#[=======================================================================[.rst:
.. command:: _modula_metadata_filter_valid_targets

  Filter valid module targets (internal function).

  .. code-block:: cmake

    _modula_metadata_filter_valid_targets(<targets> <output_var>)

  ``targets``
    List of targets
  ``output_var``
    Variable name to store valid targets

#]=======================================================================]
function(_modula_metadata_filter_valid_targets targets output_var)
    set(valid_targets "")

    foreach(target ${targets})
        if(TARGET ${target})
            get_property(module_name TARGET ${target} PROPERTY MODULA_MODULE_NAME)
            if(module_name)
                list(APPEND valid_targets ${target})
            else()
                modula_message(VERBOSE "Target '${target}' has no module name, skipping" MODULE "MetadataProcessor")
            endif()
        else()
            modula_message(VERBOSE "Target '${target}' does not exist, skipping" MODULE "MetadataProcessor")
        endif()
    endforeach()

    set(${output_var} "${valid_targets}" PARENT_SCOPE)
endfunction()

#[=======================================================================[.rst:
.. command:: _modula_metadata_add_header_info

  Add metadata header information (internal function).

  .. code-block:: cmake

    _modula_metadata_add_header_info(<json_content> <generation_time> <total_modules> <output_var>)

  ``json_content``
    Current JSON content
  ``generation_time``
    Generation timestamp
  ``total_modules``
    Total number of modules
  ``output_var``
    Variable name to store updated JSON content

#]=======================================================================]
function(_modula_metadata_add_header_info json_content generation_time total_modules output_var)
    string(JSON json_content SET "${json_content}" "metadata" "{}")
    string(JSON json_content SET "${json_content}" "metadata" "generator" "\"MetadataProcessor\"")
    string(JSON json_content SET "${json_content}" "metadata" "version" "\"${PROJECT_VERSION}\"")
    string(JSON json_content SET "${json_content}" "metadata" "generated_at" "\"${generation_time}\"")
    string(JSON json_content SET "${json_content}" "metadata" "cmake_version" "\"${CMAKE_VERSION}\"")
    string(JSON json_content SET "${json_content}" "metadata" "project" "\"${CMAKE_PROJECT_NAME}\"")
    string(JSON json_content SET "${json_content}" "metadata" "compiler" "\"${CMAKE_CXX_COMPILER_ID}\"")
    string(JSON json_content SET "${json_content}" "metadata" "compiler_version" "\"${CMAKE_CXX_COMPILER_VERSION}\"")
    string(JSON json_content SET "${json_content}" "metadata" "build_type" "\"${CMAKE_BUILD_TYPE}\"")
    string(JSON json_content SET "${json_content}" "metadata" "source_dir" "\"${CMAKE_SOURCE_DIR}\"")
    string(JSON json_content SET "${json_content}" "metadata" "binary_dir" "\"${CMAKE_BINARY_DIR}\"")
    string(JSON json_content SET "${json_content}" "metadata" "total_modules" "${total_modules}")

    set(${output_var} "${json_content}" PARENT_SCOPE)
endfunction()

#[=======================================================================[.rst:
.. command:: _modula_metadata_collect_module_info

  Collect detailed metadata information for a single module (internal function).

  .. code-block:: cmake

    _modula_metadata_collect_module_info(<target> <output_var>)

  ``target``
    Target name
  ``output_var``
    Variable name to store module metadata JSON string

  This function collects all relevant information about the target, including:
  - Basic module information (name, version, directory, interface file)
  - Source file list
  - Dependencies
  - Build configuration (target type, C++ standard, compile options)
  - Output configuration (export name, output directory)
  - Link information (link libraries, compile definitions, include directories)

#]=======================================================================]
function(_modula_metadata_collect_module_info target output_var)
    if(NOT TARGET ${target})
        set(${output_var} "" PARENT_SCOPE)
        return()
    endif()

    # Get core module properties
    get_property(module_name TARGET ${target} PROPERTY MODULA_MODULE_NAME)
    get_property(module_primary_file TARGET ${target} PROPERTY MODULA_MODULE_PRIMARY_FILE)

    if(NOT module_name)
        set(${output_var} "" PARENT_SCOPE)
        return()
    endif()

    # Build module object
    set(module_obj "{}")

    # Add basic module information
    _modula_metadata_add_basic_info("${module_obj}" "${target}" "${module_name}" "${module_primary_file}" module_obj)

    # Add source files
    _modula_metadata_add_source_files("${module_obj}" "${target}" module_obj)

    # Add dependencies
    _modula_metadata_add_dependencies("${module_obj}" "${target}" "${module_name}" module_obj)

    # Add build configuration
    _modula_metadata_add_build_config("${module_obj}" "${target}" module_obj)

    # Add output configuration
    _modula_metadata_add_output_config("${module_obj}" "${target}" module_obj)

    # Add link information
    _modula_metadata_add_link_info("${module_obj}" "${target}" module_obj)

    # Add timestamp
    string(TIMESTAMP current_time "%Y-%m-%dT%H:%M:%S" UTC)
    string(JSON module_obj SET "${module_obj}" "generated_at" "\"${current_time}\"")

    set(${output_var} "${module_obj}" PARENT_SCOPE)
endfunction()

#[=======================================================================[.rst:
.. command:: _modula_metadata_add_basic_info

  Add basic module information (internal function).

#]=======================================================================]
function(_modula_metadata_add_basic_info module_obj target module_name module_primary_file output_var)
    get_property(target_source_dir TARGET ${target} PROPERTY SOURCE_DIR)
    get_property(target_version TARGET ${target} PROPERTY VERSION)

    # Try to extract version from CMakeLists.txt if not set
    if(NOT target_version AND target_source_dir)
        _modula_metadata_extract_version("${target_source_dir}/CMakeLists.txt" target_version)
    endif()

    # Set basic fields
    string(JSON module_obj SET "${module_obj}" "name" "\"${module_name}\"")
    if(target_version)
        string(JSON module_obj SET "${module_obj}" "version" "\"${target_version}\"")
    endif()
    string(JSON module_obj SET "${module_obj}" "directory" "\"${target_source_dir}\"")
    string(JSON module_obj SET "${module_obj}" "interface" "\"${module_primary_file}\"")

    set(${output_var} "${module_obj}" PARENT_SCOPE)
endfunction()

#[=======================================================================[.rst:
.. command:: _modula_metadata_add_source_files

  Add source files information (internal function).

#]=======================================================================]
function(_modula_metadata_add_source_files module_obj target output_var)
    get_property(sources TARGET ${target} PROPERTY SOURCES)
    get_property(cxx_modules_files TARGET ${target} PROPERTY CXX_MODULE_SET_CXX_MODULES)

    string(JSON module_obj SET "${module_obj}" "sources" "[]")
    set(source_index 0)

    # Add C++ module files first
    if(cxx_modules_files)
        foreach(module_file ${cxx_modules_files})
            string(JSON module_obj SET "${module_obj}" "sources" "${source_index}" "\"${module_file}\"")
            math(EXPR source_index "${source_index} + 1")
        endforeach()
    endif()

    # Add regular source files
    if(sources)
        foreach(source_file ${sources})
            string(JSON module_obj SET "${module_obj}" "sources" "${source_index}" "\"${source_file}\"")
            math(EXPR source_index "${source_index} + 1")
        endforeach()
    endif()

    set(${output_var} "${module_obj}" PARENT_SCOPE)
endfunction()

#[=======================================================================[.rst:
.. command:: _modula_metadata_add_dependencies

  Add module dependencies information (internal function).

#]=======================================================================]
function(_modula_metadata_add_dependencies module_obj target module_name output_var)
    get_property(link_libraries TARGET ${target} PROPERTY LINK_LIBRARIES)

    set(module_dependencies "")
    if(link_libraries)
        foreach(linked_lib ${link_libraries})
            if(TARGET ${linked_lib})
                get_property(is_target_module TARGET ${linked_lib} PROPERTY MODULA_IS_MODULE)
                get_property(is_declared_module TARGET ${linked_lib} PROPERTY MODULA_DECLARED_MODULE)

                if(is_target_module OR is_declared_module)
                    get_property(linked_module_name TARGET ${linked_lib} PROPERTY MODULA_MODULE_NAME)
                    if(linked_module_name)
                        # Avoid self-dependency
                        if(NOT linked_module_name STREQUAL module_name)
                            list(APPEND module_dependencies ${linked_module_name})
                        endif()
                    else()
                        # Use target name as temporary module name if module name not set
                        if(NOT linked_lib STREQUAL target)
                            list(APPEND module_dependencies ${linked_lib})
                        endif()
                    endif()
                endif()
            endif()
        endforeach()

        if(module_dependencies)
            list(REMOVE_DUPLICATES module_dependencies)
        endif()
    endif()

    string(JSON module_obj SET "${module_obj}" "dependencies" "[]")
    if(module_dependencies)
        set(dep_index 0)
        foreach(dep ${module_dependencies})
            string(JSON module_obj SET "${module_obj}" "dependencies" "${dep_index}" "\"${dep}\"")
            math(EXPR dep_index "${dep_index} + 1")
        endforeach()
    endif()

    set(${output_var} "${module_obj}" PARENT_SCOPE)
endfunction()

#[=======================================================================[.rst:
.. command:: _modula_metadata_add_build_config

  Add build configuration information (internal function).

#]=======================================================================]
function(_modula_metadata_add_build_config module_obj target output_var)
    get_property(cxx_standard TARGET ${target} PROPERTY CXX_STANDARD)
    get_property(target_type TARGET ${target} PROPERTY TYPE)
    get_property(compile_options TARGET ${target} PROPERTY COMPILE_OPTIONS)
    get_property(interface_compile_options TARGET ${target} PROPERTY INTERFACE_COMPILE_OPTIONS)

    string(JSON module_obj SET "${module_obj}" "build_config" "{}")
    string(JSON module_obj SET "${module_obj}" "build_config" "target_name" "\"${target}\"")

    if(target_type)
        string(JSON module_obj SET "${module_obj}" "build_config" "target_type" "\"${target_type}\"")
    endif()

    if(cxx_standard)
        string(JSON module_obj SET "${module_obj}" "build_config" "cxx_standard" "${cxx_standard}")
    endif()

    # Compile options
    set(all_compile_opts "")
    if(compile_options)
        list(APPEND all_compile_opts ${compile_options})
    endif()
    if(interface_compile_options)
        list(APPEND all_compile_opts ${interface_compile_options})
    endif()

    string(JSON module_obj SET "${module_obj}" "build_config" "compile_options" "[]")
    if(all_compile_opts)
        list(REMOVE_DUPLICATES all_compile_opts)
        set(opt_index 0)
        foreach(compile_opt ${all_compile_opts})
            string(JSON module_obj SET "${module_obj}" "build_config" "compile_options" "${opt_index}" "\"${compile_opt}\"")
            math(EXPR opt_index "${opt_index} + 1")
        endforeach()
    endif()

    set(${output_var} "${module_obj}" PARENT_SCOPE)
endfunction()

#[=======================================================================[.rst:
.. command:: _modula_metadata_add_output_config

  Add output configuration information (internal function).

#]=======================================================================]
function(_modula_metadata_add_output_config module_obj target output_var)
    get_property(export_name TARGET ${target} PROPERTY EXPORT_NAME)
    get_property(output_name TARGET ${target} PROPERTY OUTPUT_NAME)
    get_property(archive_output_directory TARGET ${target} PROPERTY ARCHIVE_OUTPUT_DIRECTORY)
    get_property(library_output_directory TARGET ${target} PROPERTY LIBRARY_OUTPUT_DIRECTORY)
    get_property(runtime_output_directory TARGET ${target} PROPERTY RUNTIME_OUTPUT_DIRECTORY)

    string(JSON module_obj SET "${module_obj}" "output_config" "{}")

    if(export_name)
        string(JSON module_obj SET "${module_obj}" "output_config" "export_name" "\"${export_name}\"")
    endif()

    if(output_name)
        string(JSON module_obj SET "${module_obj}" "output_config" "output_name" "\"${output_name}\"")
    endif()

    # Output directories
    set(has_output_dirs FALSE)
    if(archive_output_directory OR library_output_directory OR runtime_output_directory)
        set(has_output_dirs TRUE)
        string(JSON module_obj SET "${module_obj}" "output_config" "output_directories" "{}")

        if(archive_output_directory)
            string(JSON module_obj SET "${module_obj}" "output_config" "output_directories" "archive" "\"${archive_output_directory}\"")
        endif()
        if(library_output_directory)
            string(JSON module_obj SET "${module_obj}" "output_config" "output_directories" "library" "\"${library_output_directory}\"")
        endif()
        if(runtime_output_directory)
            string(JSON module_obj SET "${module_obj}" "output_config" "output_directories" "runtime" "\"${runtime_output_directory}\"")
        endif()
    endif()

    if(NOT has_output_dirs AND NOT export_name AND NOT output_name)
        string(JSON module_obj SET "${module_obj}" "output_config" "configured" "false")
    else()
        string(JSON module_obj SET "${module_obj}" "output_config" "configured" "true")
    endif()

    set(${output_var} "${module_obj}" PARENT_SCOPE)
endfunction()

#[=======================================================================[.rst:
.. command:: _modula_metadata_add_link_info

  Add link information (internal function).

#]=======================================================================]
function(_modula_metadata_add_link_info module_obj target output_var)
    get_property(link_libraries TARGET ${target} PROPERTY LINK_LIBRARIES)
    get_property(interface_link_libraries TARGET ${target} PROPERTY INTERFACE_LINK_LIBRARIES)
    get_property(imported_link_interface_libraries TARGET ${target} PROPERTY IMPORTED_LINK_INTERFACE_LIBRARIES)
    get_property(compile_definitions TARGET ${target} PROPERTY COMPILE_DEFINITIONS)
    get_property(interface_compile_definitions TARGET ${target} PROPERTY INTERFACE_COMPILE_DEFINITIONS)
    get_property(include_directories TARGET ${target} PROPERTY INCLUDE_DIRECTORIES)
    get_property(interface_include_directories TARGET ${target} PROPERTY INTERFACE_INCLUDE_DIRECTORIES)

    string(JSON module_obj SET "${module_obj}" "link_info" "{}")

    # Collect all link libraries
    set(all_link_libs "")
    if(link_libraries)
        list(APPEND all_link_libs ${link_libraries})
    endif()
    if(interface_link_libraries)
        list(APPEND all_link_libs ${interface_link_libraries})
    endif()
    if(imported_link_interface_libraries)
        list(APPEND all_link_libs ${imported_link_interface_libraries})
    endif()
    if(all_link_libs)
        list(REMOVE_DUPLICATES all_link_libs)
    endif()

    string(JSON module_obj SET "${module_obj}" "link_info" "link_libraries" "[]")
    if(all_link_libs)
        set(lib_index 0)
        foreach(link_lib ${all_link_libs})
            string(JSON module_obj SET "${module_obj}" "link_info" "link_libraries" "${lib_index}" "\"${link_lib}\"")
            math(EXPR lib_index "${lib_index} + 1")
        endforeach()
    endif()

    # Compile definitions
    set(all_compile_defs "")
    if(compile_definitions)
        list(APPEND all_compile_defs ${compile_definitions})
    endif()
    if(interface_compile_definitions)
        list(APPEND all_compile_defs ${interface_compile_definitions})
    endif()
    if(all_compile_defs)
        list(REMOVE_DUPLICATES all_compile_defs)
    endif()

    string(JSON module_obj SET "${module_obj}" "link_info" "compile_definitions" "[]")
    if(all_compile_defs)
        set(def_index 0)
        foreach(compile_def ${all_compile_defs})
            string(JSON module_obj SET "${module_obj}" "link_info" "compile_definitions" "${def_index}" "\"${compile_def}\"")
            math(EXPR def_index "${def_index} + 1")
        endforeach()
    endif()

    # Include directories
    set(all_include_dirs "")
    if(include_directories)
        list(APPEND all_include_dirs ${include_directories})
    endif()
    if(interface_include_directories)
        list(APPEND all_include_dirs ${interface_include_directories})
    endif()
    if(all_include_dirs)
        list(REMOVE_DUPLICATES all_include_dirs)
    endif()

    string(JSON module_obj SET "${module_obj}" "link_info" "include_directories" "[]")
    if(all_include_dirs)
        set(dir_index 0)
        foreach(include_dir ${all_include_dirs})
            string(JSON module_obj SET "${module_obj}" "link_info" "include_directories" "${dir_index}" "\"${include_dir}\"")
            math(EXPR dir_index "${dir_index} + 1")
        endforeach()
    endif()

    set(${output_var} "${module_obj}" PARENT_SCOPE)
endfunction()

#[=======================================================================[.rst:
.. command:: _modula_metadata_extract_version

  Extract version from CMakeLists.txt file (internal function).

#]=======================================================================]
function(_modula_metadata_extract_version cmake_file output_var)
    if(NOT EXISTS "${cmake_file}")
        set(${output_var} "" PARENT_SCOPE)
        return()
    endif()

    file(READ "${cmake_file}" cmake_content)
    # Match project(...VERSION x.y.z...) pattern
    string(REGEX MATCH "project\\([^)]*VERSION[ \t\n\r]+([0-9]+\\.[0-9]+\\.[0-9]+[^) \t\n\r]*)" version_match "${cmake_content}")
    if(version_match)
        string(REGEX REPLACE "project\\([^)]*VERSION[ \t\n\r]+([0-9]+\\.[0-9]+\\.[0-9]+[^) \t\n\r]*)" "\\1" extracted_version "${version_match}")
        set(${output_var} "${extracted_version}" PARENT_SCOPE)
    else()
        set(${output_var} "" PARENT_SCOPE)
    endif()
endfunction()

message(STATUS "Modula: Module metadata processor loaded")
