# Modula - 一款C++架构框架

`Modula` 是一个现代化的C++框架，用于架构设计、分析和管理复杂的应用程序。它通过将软件视为一系列定义明确、相互独立的**单元(Unit)**的集合，赋能开发者构建健壮、可扩展的系统。

`Modula` 的核心哲学是为高层级的**架构分析**和**运行时生命周期管理**提供工具，而非干涉底层的编译模型。

> **一项至关重要的区别：** `Modula Unit` (单元) 是一个**软件架构**层面的概念。它代表您应用程序中一个逻辑上的、高层级的组件（例如：一个子系统、一项服务、一个主功能库）。此概念**完全独立于**C++20语言层级的 `modules` (即 `import`/`export` 语法)，且两者**毫无关系**。无论您使用传统头文件还是C++20模块进行编译，`Modula` 都能帮助您管理您的软件架构。

## Modula的设计哲学：架构即代码

`Modula` 将您应用程序的架构视为一等公民，使其在构建时和运行时都可以被声明、被分析、被管理。

1.  **单元 (The Unit)**：`Unit` 是 `Modula` 应用程序中的基本构建块。它是一个自包含、可替换的组件，拥有明确定义的公共API和生命周期。通常，一个`Unit`对应一个CMake目标 (target)（例如一个库）。
2.  **声明式架构 (Declarative Architecture)**：您在CMake构建系统中将目标声明为`Unit`。此行为会将它们注册到`Modula`框架中。
3.  **自动化分析 (Automated Analysis)**：在CMake配置阶段，`Modula`会检查这些`Unit`，分析它们之间的相互依赖关系（基于`target_link_libraries`），并发现它们的公共API。
4.  **元数据生成 (Metadata Generation)**：分析过程会生成一个 `units.json` 元数据文件——一份您整个应用程序架构的、可被机器读取的"蓝图"。
5.  **智能化运行时 (Intelligent Runtime)**：在运行时，`Modula`的`UnitManager`会使用这份蓝图来：
    *   构建一个依赖关系图。
    *   探测循环依赖。
    *   以正确的拓扑顺序初始化所有的`Unit`。
    *   以相反的顺序提供受控的关闭流程。

## 核心特性

*   **架构单元声明**: 使用一个简单的CMake函数 `modula_declare_unit()`，清晰地划分您的逻辑组件。
*   **自动化依赖图构建**: 从您已有的`target_link_libraries`关系中自动构建依赖图，无需重复声明。
*   **构建时元数据生成**: 生成一份详尽的JSON清单，包含所有`Unit`的属性、API和依赖关系。
*   **健壮的生命周期管理**: 中央化的`UnitManager`负责以正确的顺序自动初始化和关闭所有`Unit`。
*   **非侵入式集成**: `Modula`通过分析您的构建系统来工作，而不会强制要求特定的编译器标志或项目结构。它能与您现有的CMake配置协同工作。
*   **可扩展的C++接口**: 提供清晰的C++概念 (`modula::concepts::Unit`) 和基类 (`modula::IUnit`) 来实现`Unit`的行为。

## Modula库架构

`Modula`本身被设计为一套内聚的、按命名空间组织的C++库。

| 命名空间             | 核心职责             | 主要组件                                       |
| :--------------------- | :------------------- | :--------------------------------------------- |
| **`modula`**           | 框架入口点           | `IUnit`, `UnitInfo`, 全局配置                  |
| **`modula::concepts`** | 概念与类型萃取       | `concept Unit`, `unit_traits`                  |
| **`modula::registry`** | 编译时注册         | `UnitRegistry`, `MODULA_REGISTER_UNIT`宏       |
| **`modula::metadata`** | 元数据结构与加载     | `UnitMetadata`, `MetadataLoader` (用于`units.json`) |
| **`modula::graph`**    | 依赖关系分析         | 拓扑排序, 循环依赖检测                         |
| **`modula::manager`**  | 运行时生命周期控制   | `UnitManager`, 初始化/关闭序列                 |

## 您项目的蓝图示例

### 1. CMake: 声明一个单元

在您的 `CMakeLists.txt` 中:

```cmake
# 将您的库添加为一个标准的CMake目标
add_library(my_awesome_unit)

target_sources(my_awesome_unit
    PRIVATE
        src/my_awesome_unit.cpp
    PUBLIC
        # 使用 FILE_SET 来声明公共API
        FILE_SET HEADERS
        BASE_DIRS "${CMAKE_CURRENT_SOURCE_DIR}/include"
        FILES include/my_awesome_unit/api.h
)

# 链接到另一个单元
target_link_libraries(my_awesome_unit PUBLIC another_unit)

# 将此目标声明为一个Modula单元
modula_declare_unit(my_awesome_unit)
```

### 2. C++: 实现一个单元

在您的 C++ 代码中:

```cpp
// include/my_awesome_unit/api.h
#pragma once
#include <modula/IUnit.h>

class MyAwesomeUnit : public modula::IUnit {
public:
    // 实现IUnit接口
    void on_initialize() override;
    void on_shutdown() override;
};

// src/my_awesome_unit.cpp
#include "my_awesome_unit/api.h"
#include <modula/registry.h>
#include <iostream>

void MyAwesomeUnit::on_initialize() {
    std::cout << "MyAwesomeUnit 已初始化!\n";
}

void MyAwesomeUnit::on_shutdown() {
    std::cout << "MyAwesomeUnit 已关闭.\n";
}

// 将此C++类注册为"my_awesome_unit"单元的实现
MODULA_REGISTER_UNIT("my_awesome_unit", MyAwesomeUnit);
```

### 3. 应用程序主函数

```cpp
#include <modula/manager.h>

int main() {
    // UnitManager会处理所有事情
    modula::UnitManager manager;

    // 加载由CMake生成的架构蓝图
    manager.load_metadata("path/to/units.json");

    // 以正确的依赖顺序初始化所有已注册的单元
    manager.initialize_all();

    // ... 您的应用程序在此运行 ...

    // 以相反的依赖顺序关闭所有单元
    manager.shutdown_all();

    return 0;
}
```

## 系统要求

- **兼容C++20/23的编译器**:
  - GCC 11+
  - Clang 14+
  - MSVC 2022+ (Visual Studio 17.0+)
- **CMake 3.23+**: 需要 `FILE_SET` 功能支持。
- **操作系统**: Windows 10+, Linux (Ubuntu 20.04+), macOS 12+ 