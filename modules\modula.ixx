/**
 * @file modula.ixx
 * @brief Modula Framework - 主模块接口
 * 
 * 这是模块化框架的主要入口点，导出所有核心功能：
 * 1. 现代C++23模块系统
 * 2. 编译期元数据自动生成
 * 3. 属性标注系统 [[modula::metadata]]
 * 4. 自动化依赖管理和拓扑排序
 * 5. 低侵入性模块定义
 * 6. 构建期自动化能力
 *
 * @version 1.0.0
 * <AUTHOR> Framework Team
 */

module;

export module modula;

// 导出子模块
export import modula.types;

export namespace modula {

/**
 * @brief 框架版本信息
 */
struct Version {
    static constexpr int major = 1;
    static constexpr int minor = 0;
    static constexpr int patch = 0;
    static constexpr const char* string = "1.0.0";
    static constexpr const char* build_date = __DATE__;
    static constexpr const char* build_time = __TIME__;
};

/**
 * @brief 获取框架版本字符串
 * @return 版本字符串
 */
constexpr const char* get_version() noexcept {
    return Version::string;
}

/**
 * @brief 获取完整版本信息
 */
constexpr const char* get_full_version() noexcept {
    return "Modula Framework v1.0.0";
}
} // namespace modula

