﻿/**
 * @file test_lib.cpp
 * @brief Implementation of TestLib utility functions
 *
 * This file contains the implementation of all utility functions declared in test_lib.h.
 * It demonstrates traditional C++ library implementation that can be used by C++20 modules.
 *
 * @version 1.0.0
 * <AUTHOR> Team
 */

#include "test_lib.h"
#include <regex>
#include <numeric>
#include <cstring>

namespace testlib {

// ============================================================================
// Math namespace implementations
// ============================================================================

namespace math {

long long factorial(int n) {
    if (n < 0) return 0;
    if (n <= 1) return 1;
    
    long long result = 1;
    for (int i = 2; i <= n; ++i) {
        result *= i;
    }
    return result;
}

int gcd(int a, int b) {
    a = std::abs(a);
    b = std::abs(b);
    
    while (b != 0) {
        int temp = b;
        b = a % b;
        a = temp;
    }
    return a;
}

int lcm(int a, int b) {
    if (a == 0 || b == 0) return 0;
    return std::abs(a * b) / gcd(a, b);
}

bool is_prime(int n) {
    if (n <= 1) return false;
    if (n <= 3) return true;
    if (n % 2 == 0 || n % 3 == 0) return false;
    
    for (int i = 5; i * i <= n; i += 6) {
        if (n % i == 0 || n % (i + 2) == 0) {
            return false;
        }
    }
    return true;
}

double power(double base, int exponent) {
    if (exponent == 0) return 1.0;
    if (exponent < 0) return 1.0 / power(base, -exponent);
    
    double result = 1.0;
    for (int i = 0; i < exponent; ++i) {
        result *= base;
    }
    return result;
}

double sqrt_newton(double x, double precision) {
    if (x < 0) return -1.0; // Invalid input
    if (x == 0) return 0.0;
    
    double guess = x / 2.0;
    double prev_guess;
    
    do {
        prev_guess = guess;
        guess = (guess + x / guess) / 2.0;
    } while (std::abs(guess - prev_guess) > precision);
    
    return guess;
}

} // namespace math

// ============================================================================
// String utilities namespace implementations
// ============================================================================

namespace string_utils {

std::string to_upper(const std::string& str) {
    std::string result = str;
    // std::transform(result.begin(), result.end(), result.begin(), ::toupper);
    return result;
}

std::string to_lower(const std::string& str) {
    std::string result = str;
    // std::transform(result.begin(), result.end(), result.begin(), ::tolower);
    return result;
}

std::string trim(const std::string& str) {
    const std::string whitespace = " \t\n\r\f\v";
    
    size_t start = str.find_first_not_of(whitespace);
    if (start == std::string::npos) return "";
    
    size_t end = str.find_last_not_of(whitespace);
    return str.substr(start, end - start + 1);
}

std::vector<std::string> split(const std::string& str, char delimiter) {
    std::vector<std::string> result;
    std::stringstream ss(str);
    std::string item;
    
    while (std::getline(ss, item, delimiter)) {
        result.push_back(item);
    }
    
    return result;
}

std::string join(const std::vector<std::string>& strings, const std::string& delimiter) {
    if (strings.empty()) return "";
    
    std::ostringstream oss;
    oss << strings[0];
    
    for (size_t i = 1; i < strings.size(); ++i) {
        oss << delimiter << strings[i];
    }
    
    return oss.str();
}

bool starts_with(const std::string& str, const std::string& prefix) {
    if (prefix.length() > str.length()) return false;
    return str.substr(0, prefix.length()) == prefix;
}

bool ends_with(const std::string& str, const std::string& suffix) {
    if (suffix.length() > str.length()) return false;
    return str.substr(str.length() - suffix.length()) == suffix;
}

std::string replace_all(const std::string& str, const std::string& from, const std::string& to) {
    if (from.empty()) return str;
    
    std::string result = str;
    size_t pos = 0;
    
    while ((pos = result.find(from, pos)) != std::string::npos) {
        result.replace(pos, from.length(), to);
        pos += to.length();
    }
    
    return result;
}

} // namespace string_utils

// ============================================================================
// Validation namespace implementations
// ============================================================================

namespace validation {

bool is_valid_email(const std::string& email) {
    const std::regex email_pattern(R"([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})");
    return std::regex_match(email, email_pattern);
}

bool is_numeric(const std::string& str) {
    if (str.empty()) return false;
    
    size_t start = 0;
    if (str[0] == '+' || str[0] == '-') {
        start = 1;
        if (str.length() == 1) return false;
    }
    
    for (size_t i = start; i < str.length(); ++i) {
        if (!std::isdigit(str[i])) return false;
    }
    
    return true;
}

bool is_valid_ipv4(const std::string& ip) {
    const std::regex ipv4_pattern(R"(^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$)");
    std::smatch match;
    
    if (!std::regex_match(ip, match, ipv4_pattern)) {
        return false;
    }
    
    for (int i = 1; i <= 4; ++i) {
        int octet = std::stoi(match[i].str());
        if (octet < 0 || octet > 255) {
            return false;
        }
    }
    
    return true;
}

int password_strength(const std::string& password) {
    int score = 0;
    
    // Length check
    if (password.length() >= 8) score++;
    if (password.length() >= 12) score++;
    
    // Character type checks
    bool has_lower = false, has_upper = false, has_digit = false, has_special = false;
    
    for (char c : password) {
        if (std::islower(c)) has_lower = true;
        else if (std::isupper(c)) has_upper = true;
        else if (std::isdigit(c)) has_digit = true;
        else has_special = true;
    }
    
    if (has_lower) score++;
    if (has_upper) score++;
    if (has_digit) score++;
    if (has_special) score++;
    
    return std::min(score, 5);
}

} // namespace validation

// ============================================================================
// Performance namespace implementations
// ============================================================================

namespace performance {

void Timer::start() {
    start_time_ = std::chrono::high_resolution_clock::now();
    is_running_ = true;
}

double Timer::stop() {
    if (!is_running_) return 0.0;
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time_);
    is_running_ = false;
    
    return duration.count() / 1000.0; // Convert to milliseconds
}

double Timer::elapsed() const {
    if (!is_running_) return 0.0;
    
    auto current_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(current_time - start_time_);
    
    return duration.count() / 1000.0; // Convert to milliseconds
}

void Timer::reset() {
    is_running_ = false;
}

std::string format_duration(double milliseconds) {
    std::ostringstream oss;
    
    if (milliseconds < 1.0) {
        oss << std::fixed << std::setprecision(3) << (milliseconds * 1000.0) << " μs";
    } else if (milliseconds < 1000.0) {
        oss << std::fixed << std::setprecision(3) << milliseconds << " ms";
    } else if (milliseconds < 60000.0) {
        oss << std::fixed << std::setprecision(3) << (milliseconds / 1000.0) << " s";
    } else {
        double minutes = milliseconds / 60000.0;
        oss << std::fixed << std::setprecision(2) << minutes << " min";
    }
    
    return oss.str();
}

double calculate_ops_per_second(long long operations, double duration_ms) {
    if (duration_ms <= 0.0) return 0.0;
    return (operations * 1000.0) / duration_ms;
}

} // namespace performance

// ============================================================================
// Data structures namespace implementations
// ============================================================================

namespace data_structures {

void Statistics::add_value(double value) {
    values_.push_back(value);
}

double Statistics::mean() const {
    if (values_.empty()) return 0.0;

    double sum = std::accumulate(values_.begin(), values_.end(), 0.0);
    return sum / values_.size();
}

double Statistics::median() const {
    if (values_.empty()) return 0.0;

    std::vector<double> sorted_values = values_;
    std::sort(sorted_values.begin(), sorted_values.end());

    size_t size = sorted_values.size();
    if (size % 2 == 0) {
        return (sorted_values[size / 2 - 1] + sorted_values[size / 2]) / 2.0;
    } else {
        return sorted_values[size / 2];
    }
}

double Statistics::standard_deviation() const {
    if (values_.size() <= 1) return 0.0;

    double mean_val = mean();
    double sum_squared_diff = 0.0;

    for (double value : values_) {
        double diff = value - mean_val;
        sum_squared_diff += diff * diff;
    }

    return std::sqrt(sum_squared_diff / (values_.size() - 1));
}

double Statistics::min() const {
    if (values_.empty()) return 0.0;
    return *std::min_element(values_.begin(), values_.end());
}

double Statistics::max() const {
    if (values_.empty()) return 0.0;
    return *std::max_element(values_.begin(), values_.end());
}

size_t Statistics::count() const {
    return values_.size();
}

void Statistics::clear() {
    values_.clear();
}

} // namespace data_structures

} // namespace testlib
