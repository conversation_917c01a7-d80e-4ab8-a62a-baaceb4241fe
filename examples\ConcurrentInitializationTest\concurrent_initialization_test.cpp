﻿/**
 * @file concurrent_initialization_test.cpp
 * @brief 真实的并发初始化测试程序
 *
 * 测试ModuleManager的并发初始化功能，对比顺序和并发初始化的性能差异。
 * 使用真实的modula::ModuleManager和实际的模块系统。
 */

#include <iostream>
#include <chrono>
#include <iomanip>
#include <thread>

// 导入modula框架
import modula.manager;
import modula.registry;

// 导入并发初始化策略
import concurrent_strategy;

// 导入所有测试模块
import CoreModule;
import DatabaseModule;
import NetworkModule;
import CacheModule;
import ServiceModule;
import LoggingModule;
import UIModule;

/**
 * @brief 手动注册所有测试模块
 *
 * 由于静态初始化的不确定性，我们在测试开始时手动注册所有模块
 */
void register_test_modules() {
    std::cout << "\n=== 注册测试模块 ===" << std::endl;

    // 注册所有测试模块
    bool success = true;
    success &= modula::register_module<CoreModule>();
    success &= modula::register_module<DatabaseModule>();
    success &= modula::register_module<NetworkModule>();
    success &= modula::register_module<CacheModule>();
    success &= modula::register_module<ServiceModule>();
    success &= modula::register_module<LoggingModule>();
    success &= modula::register_module<UIModule>();

    if (success) {
        std::cout << "所有模块注册成功" << std::endl;
    } else {
        std::cout << "部分模块注册失败" << std::endl;
    }
}

/**
 * @brief 性能测试辅助类
 */
class PerformanceTimer {
public:
    void start() {
        start_time_ = std::chrono::high_resolution_clock::now();
    }
    
    void stop() {
        end_time_ = std::chrono::high_resolution_clock::now();
    }
    
    [[nodiscard]] double get_duration_ms() const {
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time_ - start_time_);
        return duration.count() / 1000.0;
    }

private:
    std::chrono::high_resolution_clock::time_point start_time_;
    std::chrono::high_resolution_clock::time_point end_time_;
};

/**
 * @brief 测试顺序初始化
 */
void test_sequential_initialization() {
    std::cout << "\n=== 测试顺序初始化（传统策略） ===" << std::endl;

    // 使用标准的模块管理器
    auto& manager = modula::ModuleManager::instance();

    // 注意：配置功能已简化，直接使用标准初始化
    PerformanceTimer timer;
    timer.start();

    bool success = manager.initialize_all();

    timer.stop();

    std::cout << "顺序初始化结果: " << (success ? "成功" : "失败") << std::endl;
    std::cout << "顺序初始化耗时: " << std::fixed << std::setprecision(2)
              << timer.get_duration_ms() << " ms" << std::endl;

    // 显示基本统计（性能统计功能已简化）
    // std::cout << "初始化模块数: " << manager.size() << std::endl;

    // 关闭所有模块
    manager.shutdown_all();
}

/**
 * @brief 测试并发初始化
 */
void test_concurrent_initialization() {
    std::cout << "\n=== 测试并发初始化（并发策略） ===" << std::endl;

    // 使用并发初始化策略
    auto& manager = concurrent_test::ConcurrentModuleManager::instance();

    // 注意：并发初始化功能已简化，使用标准初始化

    PerformanceTimer timer;
    timer.start();

    bool success = manager.initialize_all();

    timer.stop();

    std::cout << "初始化结果: " << (success ? "成功" : "失败") << std::endl;
    std::cout << "初始化耗时: " << std::fixed << std::setprecision(2)
              << timer.get_duration_ms() << " ms" << std::endl;

    // 显示基本统计
    // std::cout << "初始化模块数: " << manager.size() << std::endl;

    // 关闭所有模块
    manager.shutdown_all();
}

/**
 * @brief 测试模块功能
 */
void test_module_functionality() {
    std::cout << "\n=== 测试模块功能 ===" << std::endl;
    
    auto& manager = modula::ModuleManager::instance();
    
    // 初始化所有模块
    if (!manager.initialize_all()) {
        std::cerr << "模块初始化失败" << std::endl;
        return;
    }
    
    try {
        // 测试各模块功能
        // 注意：这里需要等待实际的模块获取API实现
        std::cout << "模块功能测试需要等待modula.manager的get_module API实现" << std::endl;
        std::cout << "当前测试验证了模块的初始化和关闭流程" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "模块功能测试失败: " << e.what() << std::endl;
    }
    
    // 关闭所有模块
    manager.shutdown_all();
}

/**
 * @brief 主函数
 */
int main() {
    try {
        std::cout << "ModuleManager 并发初始化测试程序" << std::endl;
        std::cout << "======================================" << std::endl;

        // 手动注册所有测试模块
        register_test_modules();

        // 检查注册表状态
        auto& registry = modula::ModuleRegistry::instance();
        auto registrations = registry.get_all_registrations();
        std::cout << "\n=== 注册表状态 ===" << std::endl;
        std::cout << "已注册模块数量: " << registrations.size() << std::endl;

        if (registrations.empty()) {
            std::cout << "警告：没有模块被注册到注册表中！" << std::endl;
            std::cout << "这可能是因为模块注册文件没有被正确链接。" << std::endl;
        } else {
            std::cout << "已注册的模块:" << std::endl;
            for (const auto& [type_key, registration] : registrations) {
                std::cout << "  - " << type_key.name() << std::endl;
            }
        }
        
        // 测试顺序初始化
        test_sequential_initialization();
        
        // 测试并发初始化
        test_concurrent_initialization();
        
        // 测试模块功能
        test_module_functionality();
        
        std::cout << "\n=== 测试完成 ===" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "测试过程中发生异常: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
