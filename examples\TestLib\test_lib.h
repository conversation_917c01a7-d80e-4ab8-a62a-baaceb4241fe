/**
 * @file test_lib.h
 * @brief TestLib - Traditional C++ utility library for demonstrating interoperability with C++20 modules
 *
 * This is a traditional C++ library (non-module) that provides utility functions
 * for mathematical calculations, string processing, and other common operations.
 * It demonstrates how C++20 modules can seamlessly integrate with traditional libraries.
 *
 * Features:
 * - Mathematical utility functions
 * - String processing utilities
 * - Data validation functions
 * - Performance measurement utilities
 *
 * @version 1.0.0
 * <AUTHOR> Team
 */

#pragma once

#include <string>
#include <vector>
#include <chrono>
#include <cmath>
#include <algorithm>
#include <sstream>
#include <iomanip>

namespace testlib {

/**
 * @namespace math
 * @brief Mathematical utility functions
 */
namespace math {

/**
 * @brief Calculate the factorial of a number
 * @param n The number to calculate factorial for
 * @return The factorial of n
 */
long long factorial(int n);

/**
 * @brief Calculate the greatest common divisor of two numbers
 * @param a First number
 * @param b Second number
 * @return The GCD of a and b
 */
int gcd(int a, int b);

/**
 * @brief Calculate the least common multiple of two numbers
 * @param a First number
 * @param b Second number
 * @return The LCM of a and b
 */
int lcm(int a, int b);

/**
 * @brief Check if a number is prime
 * @param n The number to check
 * @return true if n is prime, false otherwise
 */
bool is_prime(int n);

/**
 * @brief Calculate the power of a number
 * @param base The base number
 * @param exponent The exponent
 * @return base raised to the power of exponent
 */
double power(double base, int exponent);

/**
 * @brief Calculate the square root using Newton's method
 * @param x The number to calculate square root for
 * @param precision The precision of the calculation
 * @return The square root of x
 */
double sqrt_newton(double x, double precision = 1e-10);

} // namespace math

/**
 * @namespace string_utils
 * @brief String processing utility functions
 */
namespace string_utils {

/**
 * @brief Convert string to uppercase
 * @param str The string to convert
 * @return The uppercase version of the string
 */
std::string to_upper(const std::string& str);

/**
 * @brief Convert string to lowercase
 * @param str The string to convert
 * @return The lowercase version of the string
 */
std::string to_lower(const std::string& str);

/**
 * @brief Trim whitespace from both ends of a string
 * @param str The string to trim
 * @return The trimmed string
 */
std::string trim(const std::string& str);

/**
 * @brief Split a string by delimiter
 * @param str The string to split
 * @param delimiter The delimiter to split by
 * @return Vector of split strings
 */
std::vector<std::string> split(const std::string& str, char delimiter);

/**
 * @brief Join strings with a delimiter
 * @param strings The strings to join
 * @param delimiter The delimiter to use
 * @return The joined string
 */
std::string join(const std::vector<std::string>& strings, const std::string& delimiter);

/**
 * @brief Check if a string starts with a prefix
 * @param str The string to check
 * @param prefix The prefix to look for
 * @return true if str starts with prefix, false otherwise
 */
bool starts_with(const std::string& str, const std::string& prefix);

/**
 * @brief Check if a string ends with a suffix
 * @param str The string to check
 * @param suffix The suffix to look for
 * @return true if str ends with suffix, false otherwise
 */
bool ends_with(const std::string& str, const std::string& suffix);

/**
 * @brief Replace all occurrences of a substring
 * @param str The original string
 * @param from The substring to replace
 * @param to The replacement substring
 * @return The string with replacements made
 */
std::string replace_all(const std::string& str, const std::string& from, const std::string& to);

} // namespace string_utils

/**
 * @namespace validation
 * @brief Data validation utility functions
 */
namespace validation {

/**
 * @brief Check if a string is a valid email address
 * @param email The email string to validate
 * @return true if valid email format, false otherwise
 */
bool is_valid_email(const std::string& email);

/**
 * @brief Check if a string contains only digits
 * @param str The string to check
 * @return true if contains only digits, false otherwise
 */
bool is_numeric(const std::string& str);

/**
 * @brief Check if a string is a valid IPv4 address
 * @param ip The IP string to validate
 * @return true if valid IPv4 format, false otherwise
 */
bool is_valid_ipv4(const std::string& ip);

/**
 * @brief Validate password strength
 * @param password The password to validate
 * @return Score from 0-5 indicating password strength
 */
int password_strength(const std::string& password);

} // namespace validation

/**
 * @namespace performance
 * @brief Performance measurement utilities
 */
namespace performance {

/**
 * @brief Simple timer class for measuring execution time
 */
class Timer {
public:
    /**
     * @brief Start the timer
     */
    void start();

    /**
     * @brief Stop the timer and return elapsed time
     * @return Elapsed time in milliseconds
     */
    double stop();

    /**
     * @brief Get elapsed time without stopping the timer
     * @return Elapsed time in milliseconds
     */
    double elapsed() const;

    /**
     * @brief Reset the timer
     */
    void reset();

private:
    std::chrono::high_resolution_clock::time_point start_time_;
    bool is_running_ = false;
};

/**
 * @brief Format time duration in human-readable format
 * @param milliseconds Time in milliseconds
 * @return Formatted time string
 */
std::string format_duration(double milliseconds);

/**
 * @brief Calculate operations per second
 * @param operations Number of operations performed
 * @param duration_ms Duration in milliseconds
 * @return Operations per second
 */
double calculate_ops_per_second(long long operations, double duration_ms);

} // namespace performance

/**
 * @namespace data_structures
 * @brief Simple data structure utilities
 */
namespace data_structures {

/**
 * @brief Simple statistics calculator
 */
class Statistics {
public:
    /**
     * @brief Add a value to the statistics
     * @param value The value to add
     */
    void add_value(double value);

    /**
     * @brief Get the mean of all values
     * @return The mean value
     */
    double mean() const;

    /**
     * @brief Get the median of all values
     * @return The median value
     */
    double median() const;

    /**
     * @brief Get the standard deviation
     * @return The standard deviation
     */
    double standard_deviation() const;

    /**
     * @brief Get the minimum value
     * @return The minimum value
     */
    double min() const;

    /**
     * @brief Get the maximum value
     * @return The maximum value
     */
    double max() const;

    /**
     * @brief Get the count of values
     * @return The number of values
     */
    size_t count() const;

    /**
     * @brief Clear all values
     */
    void clear();

private:
    std::vector<double> values_;
};

} // namespace data_structures

} // namespace testlib
