[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "modula-generator"
version = "1.0.0"
description = "Enhanced C++ Metadata Generation Tool for Modula Framework"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Modula Framework Team", email = "<EMAIL>"}
]
keywords = ["c++", "codegen", "metadata", "modules", "cmake", "dependency-analysis"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Topic :: Software Development :: Code Generators",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: C++",
    "Operating System :: OS Independent",
]
requires-python = ">=3.8"
dependencies = []

[project.scripts]
modula-generator = "modula_generator.__main__:console_entry"

[tool.setuptools]
packages = {find = {}}
include-package-data = true


