# ==============================================================================
# AnalyzerProcessor.cmake - Module Analysis Processor
# ==============================================================================
#
# Analyzes C++ module files to extract module information.
# Supports incremental analysis with caching.

cmake_minimum_required(VERSION 3.28)
include_guard(GLOBAL)

#[=======================================================================[.rst:
AnalyzerProcessor - Module Analysis Processor
============================================

Provides module analysis functionality for the Modula Framework.

Core Responsibilities:
- Parse C++ module files to extract module information
- Identify module names, imports, and exports
- Support caching
- Set target properties with analysis results

Main Interface:
- ``modula_process_analysis()`` - Process target list for module analysis

#]=======================================================================]

# ==============================================================================
# Dependencies
# ==============================================================================

include("${CMAKE_CURRENT_LIST_DIR}/../ModulaConfiguration.cmake")
include("${CMAKE_CURRENT_LIST_DIR}/../ModulaUtils.cmake")

# ==============================================================================
# Processor Registration
# ==============================================================================

if(COMMAND modula_register_processor)
    modula_register_processor("AnalyzerProcessor" "modula_process_analysis")
endif()

# ==============================================================================
# Analysis Configuration
# ==============================================================================

# Regex patterns for C++ module analysis
set(_MODULA_ANALYZER_MODULE_PATTERN "export[ \\t]+module[ \\t]+([a-zA-Z_][a-zA-Z0-9_.]*)" CACHE INTERNAL "Regex for C++ module declaration")
set(_MODULA_ANALYZER_IMPORT_PATTERN "import[ \\t]+([a-zA-Z_][a-zA-Z0-9_.]*)" CACHE INTERNAL "Regex for C++ module import")
set(_MODULA_ANALYZER_EXPORT_PATTERN "export[ \\t]+([^;]+)" CACHE INTERNAL "Regex for C++ module export")

# ==============================================================================
# Main Processing Interface
# ==============================================================================

#[=======================================================================[.rst:
.. command:: modula_process_analysis

  Process target list for module analysis.

  .. code-block:: cmake

    modula_process_analysis(<targets>)

  ``targets``
    List of targets to analyze

  Analyzes the specified targets to extract module information including
  module names, imports, and exports. Results are cached for performance
  and set as target properties.
#]=======================================================================]
function(modula_process_analysis targets)
    if(NOT targets)
        modula_message(VERBOSE "No targets provided for analysis" MODULE "AnalyzerProcessor")
        return()
    endif()

    modula_message(VERBOSE "Processing analysis for targets: ${targets}" MODULE "AnalyzerProcessor")

    set(processed_count 0)
    set(failed_count 0)

    foreach(target_name ${targets})
        if(NOT TARGET ${target_name})
            modula_message(WARNING "Target '${target_name}' does not exist" MODULE "AnalyzerProcessor")
            math(EXPR failed_count "${failed_count} + 1")
            continue()
        endif()

        # Check cache validity
        _modula_analyzer_check_cache("${target_name}" cache_valid)
        if(cache_valid)
            modula_message(VERBOSE "Using cached analysis for '${target_name}'" MODULE "AnalyzerProcessor")
            math(EXPR processed_count "${processed_count} + 1")
            continue()
        endif()

        # Perform analysis
        _modula_analyzer_process_target("${target_name}" result)
        if(result)
            math(EXPR processed_count "${processed_count} + 1")
        else()
            math(EXPR failed_count "${failed_count} + 1")
        endif()
    endforeach()

    modula_message(VERBOSE "Analysis completed: ${processed_count} processed, ${failed_count} failed" MODULE "AnalyzerProcessor")
endfunction()

# ==============================================================================
# Cache Management
# ==============================================================================

#[=======================================================================[.rst:
.. command:: _modula_analyzer_check_cache

  Check if target has valid cached analysis results.

  .. code-block:: cmake

    _modula_analyzer_check_cache(<target_name> <output_var>)

  ``target_name``
    Target name to check
  ``output_var``
    Variable to store cache validity result

  Private function that checks if cached analysis results are valid
  and applies them if found.
#]=======================================================================]
function(_modula_analyzer_check_cache target_name output_var)
    modula_cache_get("${target_name}" cached_result NAMESPACE "analyzer")
    if(NOT cached_result)
        set(${output_var} FALSE PARENT_SCOPE)
        return()
    endif()

    # Verify primary file still exists
    get_property(primary_file TARGET ${target_name} PROPERTY MODULA_MODULE_PRIMARY_FILE)
    if(primary_file AND EXISTS "${primary_file}")
        _modula_analyzer_apply_cached_result("${target_name}" "${cached_result}")
        set(${output_var} TRUE PARENT_SCOPE)
    else()
        # Invalidate cache if file is missing
        modula_cache_set("${target_name}" "" NAMESPACE "analyzer")
        set(${output_var} FALSE PARENT_SCOPE)
    endif()
endfunction()

#[=======================================================================[.rst:
.. command:: _modula_analyzer_apply_cached_result

  Apply cached analysis results to target properties.

  .. code-block:: cmake

    _modula_analyzer_apply_cached_result(<target_name> <cached_result>)

  ``target_name``
    Target name
  ``cached_result``
    Cached result string in format "module_name:imports:exports"

  Private function that parses cached results and sets target properties.
#]=======================================================================]
function(_modula_analyzer_apply_cached_result target_name cached_result)
    string(REPLACE ":" ";" result_parts "${cached_result}")
    list(GET result_parts 0 module_name)

    list(LENGTH result_parts result_parts_len)

    # Handle imports and exports safely
    if(result_parts_len GREATER 1)
        list(GET result_parts 1 imports)
    else()
        set(imports "")
    endif()
    
    if(result_parts_len GREATER 2)
        list(GET result_parts 2 exports)
    else()
        set(exports "")
    endif()

    _modula_analyzer_set_target_properties("${target_name}" "${module_name}" "${imports}" "${exports}")
endfunction()

# ==============================================================================
# Target Analysis
# ==============================================================================

#[=======================================================================[.rst:
.. command:: _modula_analyzer_process_target

  Process a single target for module analysis.

  .. code-block:: cmake

    _modula_analyzer_process_target(<target_name> <output_var>)

  ``target_name``
    Target name to process
  ``output_var``
    Variable to store processing result

  Private function that performs the actual analysis of a target's
  primary module file.
#]=======================================================================]
function(_modula_analyzer_process_target target_name output_var)
    # Get target module information
    get_property(interface_file TARGET ${target_name} PROPERTY MODULA_MODULE_PRIMARY_FILE)
    get_property(expected_module_name TARGET ${target_name} PROPERTY MODULA_MODULE_NAME)

    if(NOT interface_file)
        modula_message(VERBOSE "No interface file for '${target_name}', setting empty properties" MODULE "AnalyzerProcessor")
        _modula_analyzer_set_empty_properties("${target_name}")
        set(${output_var} TRUE PARENT_SCOPE)
        return()
    endif()

    if(NOT EXISTS "${interface_file}")
        modula_message(WARNING "Interface file does not exist: ${interface_file}" MODULE "AnalyzerProcessor")
        _modula_analyzer_set_empty_properties("${target_name}")
        set(${output_var} FALSE PARENT_SCOPE)
        return()
    endif()

    # Analyze the file
    _modula_analyzer_analyze_file("${interface_file}" "${expected_module_name}"
                                  module_name imports exports)

    # Set target properties
    _modula_analyzer_set_target_properties("${target_name}" "${module_name}" "${imports}" "${exports}")

    # Cache results
    set(cache_value "${module_name}:${imports}:${exports}")
    string(REGEX REPLACE "[\\r\\n]+" " " cache_value "${cache_value}")
    modula_cache_set("${target_name}" "${cache_value}" NAMESPACE "analyzer")

    modula_message(VERBOSE "Analyzed module '${module_name}' in '${interface_file}'" MODULE "AnalyzerProcessor")
    set(${output_var} TRUE PARENT_SCOPE)
endfunction()

# ==============================================================================
# File Analysis
# ==============================================================================

#[=======================================================================[.rst:
.. command:: _modula_analyzer_analyze_file

  Analyze a module file to extract module information.

  .. code-block:: cmake

    _modula_analyzer_analyze_file(<file_path> <expected_name> 
                                  <module_name_var> <imports_var> <exports_var>)

  ``file_path``
    Path to the module file
  ``expected_name``
    Expected module name (fallback)
  ``module_name_var``
    Variable to store extracted module name
  ``imports_var``
    Variable to store extracted imports
  ``exports_var``
    Variable to store extracted exports

  Private function that performs regex-based analysis of module files.
#]=======================================================================]
function(_modula_analyzer_analyze_file file_path expected_module_name 
         module_name_var imports_var exports_var)
    
    # Read file content with size limit for performance
    if(NOT EXISTS "${file_path}")
        set(file_content "")
    else()
        file(READ "${file_path}" file_content LIMIT 2048 ENCODING UTF8)
    endif()

    # Extract module name
    set(module_name "")
    string(REGEX MATCH "${_MODULA_ANALYZER_MODULE_PATTERN}" module_match "${file_content}")
    if(module_match)
        string(REGEX REPLACE "${_MODULA_ANALYZER_MODULE_PATTERN}" "\\1" module_name "${module_match}")
        string(STRIP "${module_name}" module_name)
    else()
        modula_message(WARNING "Could not find 'export module' declaration in file: ${file_path}. The file might be malformed or not a C++20 module interface." MODULE "AnalyzerProcessor")
    endif()

    # Use expected name as fallback
    if(NOT module_name)
        modula_message(VERBOSE "Using fallback module name '${expected_module_name}' for file: ${file_path}" MODULE "AnalyzerProcessor")
        set(module_name "${expected_module_name}")
    endif()

    # Extract imports
    set(imports "")
    string(REGEX MATCHALL "${_MODULA_ANALYZER_IMPORT_PATTERN}" import_matches "${file_content}")
    foreach(import_match ${import_matches})
        string(REGEX REPLACE "${_MODULA_ANALYZER_IMPORT_PATTERN}" "\\1" import_name "${import_match}")
        string(STRIP "${import_name}" import_name)
        if(import_name)
            list(APPEND imports "${import_name}")
        endif()
    endforeach()

    # Extract exports
    set(exports "")
    string(REGEX MATCHALL "${_MODULA_ANALYZER_EXPORT_PATTERN}" export_matches "${file_content}")
    foreach(export_match ${export_matches})
        string(REGEX REPLACE "^export[ \\t]+" "" export_content "${export_match}")
        string(STRIP "${export_content}" export_content)
        if(export_content AND NOT export_content MATCHES "^module")
            list(APPEND exports "${export_content}")
        endif()
    endforeach()

    # Remove duplicates
    if(imports)
        list(REMOVE_DUPLICATES imports)
    endif()
    if(exports)
        list(REMOVE_DUPLICATES exports)
    endif()

    # Return results
    set(${module_name_var} "${module_name}" PARENT_SCOPE)
    set(${imports_var} "${imports}" PARENT_SCOPE)
    set(${exports_var} "${exports}" PARENT_SCOPE)
endfunction()

# ==============================================================================
# Property Management
# ==============================================================================

#[=======================================================================[.rst:
.. command:: _modula_analyzer_set_target_properties

  Set module analysis results as target properties.

  .. code-block:: cmake

    _modula_analyzer_set_target_properties(<target_name> <module_name> 
                                           <imports> <exports>)

  ``target_name``
    Target name
  ``module_name``
    Module name
  ``imports``
    List of imports
  ``exports``
    List of exports

  Private function that sets standardized target properties with analysis results.
#]=======================================================================]
function(_modula_analyzer_set_target_properties target_name module_name imports exports)
    set_property(TARGET ${target_name} PROPERTY MODULA_MODULE_NAME "${module_name}")
    set_property(TARGET ${target_name} PROPERTY MODULA_MODULE_IMPORTS "${imports}")
    set_property(TARGET ${target_name} PROPERTY MODULA_MODULE_EXPORTS "${exports}")
    set_property(TARGET ${target_name} PROPERTY MODULA_MODULE_ANALYZED TRUE)

    string(TIMESTAMP current_timestamp)
    set_property(TARGET ${target_name} PROPERTY MODULA_MODULE_ANALYSIS_TIMESTAMP "${current_timestamp}")
endfunction()

#[=======================================================================[.rst:
.. command:: _modula_analyzer_set_empty_properties

  Set empty module properties for targets without module files.

  .. code-block:: cmake

    _modula_analyzer_set_empty_properties(<target_name>)

  ``target_name``
    Target name

  Private function that sets empty properties for targets that don't have
  valid module files.
#]=======================================================================]
function(_modula_analyzer_set_empty_properties target_name)
    set_property(TARGET ${target_name} PROPERTY MODULA_MODULE_NAME "")
    set_property(TARGET ${target_name} PROPERTY MODULA_MODULE_IMPORTS "")
    set_property(TARGET ${target_name} PROPERTY MODULA_MODULE_EXPORTS "")
    set_property(TARGET ${target_name} PROPERTY MODULA_MODULE_ANALYZED TRUE)

    string(TIMESTAMP current_timestamp)
    set_property(TARGET ${target_name} PROPERTY MODULA_MODULE_ANALYSIS_TIMESTAMP "${current_timestamp}")
endfunction()

message(STATUS "Modula: Module analysis processor loaded")
