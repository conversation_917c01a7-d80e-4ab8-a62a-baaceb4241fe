/**
 * @file modula.concurrent_initializer.ixx
 * @brief 并发初始化器 - 基于并行组的模块并发初始化
 *
 * 提供基于编译时并行组信息的模块并发初始化功能，
 * 使用现代C++20/23特性实现零开销的并发调度。
 *
 * @version 1.0.0
 */

module;

#include <memory>
#include <mutex>
#include <thread>
#include <barrier>
#include <atomic>
#include <array>
#include <chrono>
#include <exception>
#include <future>
#include <stop_token>
#include <vector>
#include <functional>
#include <string>
#include <iostream>
#include <typeindex>
#include <unordered_set>
#include <unordered_map>
#include <algorithm>

export module modula.concurrent_initializer;

import modula.types;
import modula.registry;

export namespace modula {

/**
 * @brief 运行时并行执行计划
 */
struct RuntimeExecutionPlan {
    std::vector<std::vector<RuntimeModuleInfo>> parallel_groups; ///< 按层级组织的并行组
    std::size_t total_modules = 0;                              ///< 总模块数
    std::size_t max_group_size = 0;                             ///< 最大组大小

    /**
     * @brief 添加并行组
     */
    void add_group(std::vector<RuntimeModuleInfo> group) {
        if (!group.empty()) {
            total_modules += group.size();
            max_group_size = std::max(max_group_size, group.size());
            parallel_groups.emplace_back(std::move(group));
        }
    }

    /**
     * @brief 获取层级数
     */
    [[nodiscard]] std::size_t levels() const noexcept {
        return parallel_groups.size();
    }

    /**
     * @brief 检查是否为空
     */
    [[nodiscard]] bool empty() const noexcept {
        return parallel_groups.empty();
    }
};

/**
 * @brief 并行初始化统计信息
 */
struct ParallelInitializationStats {
    std::chrono::milliseconds total_time{0};           ///< 总初始化时间
    std::chrono::milliseconds max_level_time{0};       ///< 最长层级时间
    std::size_t total_modules = 0;                     ///< 总模块数
    std::size_t parallel_levels = 0;                   ///< 并行层级数
    std::size_t max_concurrent_modules = 0;            ///< 最大并发模块数
    std::size_t failed_modules = 0;                    ///< 失败模块数
    double average_concurrency = 0.0;                  ///< 平均并发度

    /**
     * @brief 重置统计信息
     */
    void reset() noexcept {
        total_time = std::chrono::milliseconds{0};
        max_level_time = std::chrono::milliseconds{0};
        total_modules = 0;
        parallel_levels = 0;
        max_concurrent_modules = 0;
        failed_modules = 0;
        average_concurrency = 0.0;
    }

    /**
     * @brief 计算成功率
     */
    [[nodiscard]] double success_rate() const noexcept {
        return total_modules > 0 ?
            static_cast<double>(total_modules - failed_modules) / total_modules : 0.0;
    }
};

/**
 * @brief 并行初始化异常
 */
class ParallelInitializationException : public std::runtime_error {
public:
    explicit ParallelInitializationException(const std::string& message)
        : std::runtime_error(message) {}

    explicit ParallelInitializationException(const std::string& message,
                                            std::size_t failed_count)
        : std::runtime_error(message), failed_modules_(failed_count) {}

    [[nodiscard]] std::size_t failed_modules() const noexcept {
        return failed_modules_;
    }

private:
    std::size_t failed_modules_ = 0;
};

/**
 * @brief 并发初始化器模板
 *
 * 基于编译时并行组信息实现模块的并发初始化，
 * 使用std::barrier进行层级同步，std::jthread进行并行执行。
 *
 * @tparam ExecutionPlan 并行执行计划类型（type_list of parallel groups）
 */
template<typename ExecutionPlan>
class ConcurrentInitializer {
public:
    /**
     * @brief 执行并发初始化
     *
     * @tparam ManagerType 模块管理器类型
     * @param manager 模块管理器实例
     * @return 如果所有模块初始化成功返回true
     */
    template<typename ManagerType>
    static bool execute(ManagerType& manager) {
        return execute_levels<ExecutionPlan>(manager);
    }

    /**
     * @brief 执行并发关闭
     *
     * @tparam ManagerType 模块管理器类型
     * @param manager 模块管理器实例
     * @return 如果所有模块关闭成功返回true
     */
    template<typename ManagerType>
    static bool shutdown(ManagerType& manager) {
        return shutdown_levels<ExecutionPlan>(manager);
    }

private:
    /**
     * @brief 执行所有层级的初始化
     */
    template<typename Levels, typename ManagerType>
    static bool execute_levels(ManagerType& manager) {
        bool success = true;
        
        // 使用折叠表达式遍历所有层级
        [&]<size_t... Is>(std::index_sequence<Is...>) {
            ((success = success && execute_level<typename Levels::template at<Is>>(manager)), ...);
        }(std::make_index_sequence<Levels::size>{});
        
        return success;
    }

    /**
     * @brief 执行所有层级的关闭（逆序）
     */
    template<typename Levels, typename ManagerType>
    static bool shutdown_levels(ManagerType& manager) {
        bool success = true;
        
        // 逆序关闭
        [&]<size_t... Is>(std::index_sequence<Is...>) {
            ((success = success && shutdown_level<typename Levels::template at<Levels::size - 1 - Is>>(manager)), ...);
        }(std::make_index_sequence<Levels::size>{});
        
        return success;
    }
    
    /**
     * @brief 执行单个层级的初始化
     */
    template<typename Level, typename ManagerType>
    static bool execute_level(ManagerType& manager) {
        if constexpr (Level::size == 1) {
            // 单模块直接初始化
            return manager.template initialize_module<typename Level::template at<0>>();
        } else {
            // 多模块并行初始化
            return execute_parallel_group<Level>(manager);
        }
    }

    /**
     * @brief 执行单个层级的关闭
     */
    template<typename Level, typename ManagerType>
    static bool shutdown_level(ManagerType& manager) {
        if constexpr (Level::size == 1) {
            // 单模块直接关闭
            return manager.template shutdown_module<typename Level::template at<0>>();
        } else {
            // 多模块并行关闭
            return shutdown_parallel_group<Level>(manager);
        }
    }
    
    /**
     * @brief 执行并行组的初始化
     */
    template<typename Group, typename ManagerType>
    static bool execute_parallel_group(ManagerType& manager) {
        constexpr size_t group_size = Group::size;
        
        if (group_size == 0) {
            return true;
        }
        
        if (group_size == 1) {
            // 单模块，直接初始化
            return manager.template initialize_module<typename Group::template at<0>>();
        }
        
        // 多模块并行初始化
        std::array<std::jthread, group_size> threads;
        std::array<std::atomic<bool>, group_size> results;
        std::barrier sync_point(group_size + 1); // +1 for main thread
        
        // 初始化结果数组
        for (size_t i = 0; i < group_size; ++i) {
            results[i].store(false);
        }
        
        // 启动并行初始化线程
        [&]<size_t... Is>(std::index_sequence<Is...>) {
            ((threads[Is] = std::jthread([&, Is]() {
                try {
                    using ModuleType = typename Group::template at<Is>;
                    results[Is].store(manager.template initialize_module<ModuleType>());
                } catch (...) {
                    results[Is].store(false);
                }
                sync_point.arrive_and_wait();
            })), ...);
        }(std::make_index_sequence<group_size>{});
        
        // 等待所有线程完成
        sync_point.arrive_and_wait();
        
        // 等待线程结束
        for (auto& thread : threads) {
            if (thread.joinable()) {
                thread.join();
            }
        }
        
        // 检查所有初始化是否成功
        for (const auto& result : results) {
            if (!result.load()) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * @brief 执行并行组的关闭
     */
    template<typename Group, typename ManagerType>
    static bool shutdown_parallel_group(ManagerType& manager) {
        constexpr size_t group_size = Group::size;
        
        if (group_size == 0) {
            return true;
        }
        
        if (group_size == 1) {
            // 单模块，直接关闭
            return manager.template shutdown_module<typename Group::template at<0>>();
        }
        
        // 多模块并行关闭
        std::array<std::jthread, group_size> threads;
        std::array<std::atomic<bool>, group_size> results;
        std::barrier sync_point(group_size + 1); // +1 for main thread
        
        // 初始化结果数组
        for (size_t i = 0; i < group_size; ++i) {
            results[i].store(false);
        }
        
        // 启动并行关闭线程
        [&]<size_t... Is>(std::index_sequence<Is...>) {
            ((threads[Is] = std::jthread([&, Is]() {
                try {
                    using ModuleType = typename Group::template at<Is>;
                    results[Is].store(manager.template shutdown_module<ModuleType>());
                } catch (...) {
                    results[Is].store(false);
                }
                sync_point.arrive_and_wait();
            })), ...);
        }(std::make_index_sequence<group_size>{});
        
        // 等待所有线程完成
        sync_point.arrive_and_wait();
        
        // 等待线程结束
        for (auto& thread : threads) {
            if (thread.joinable()) {
                thread.join();
            }
        }
        
        // 检查所有关闭是否成功
        for (const auto& result : results) {
            if (!result.load()) {
                return false;
            }
        }
        
        return true;
    }
};

/**
 * @brief 便捷的并发初始化器别名
 */
template<typename ExecutionPlan>
using concurrent_initializer = ConcurrentInitializer<ExecutionPlan>;

} // namespace modula
