/**
 * @file modula.manager.cpp
 * @brief 模块管理器实现
 */

#include <iostream>
#include <chrono>
#include <mutex>
#include <thread>
#include <atomic>
#include <barrier>
#include <vector>
#include <algorithm>

module modula.manager;

import modula.registry;
import modula.types;

namespace modula {

bool ModuleManager::initialize_all() {
    auto start_time = std::chrono::steady_clock::now();

    // 重置统计信息
    stats_ = PerformanceStats{};

    try {
        bool success = false;

        if (config_.enable_concurrent_initialization) {
            std::cout << "Starting parallel initialization..." << std::endl;
            success = execute_parallel_initialization();
        } else {
            std::cout << "Starting serial initialization..." << std::endl;
            success = execute_serial_initialization();
        }

        auto end_time = std::chrono::steady_clock::now();
        stats_.total_init_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

        if (config_.enable_performance_monitoring) {
            std::cout << "Initialization completed in " << stats_.total_init_time.count() << "ms" << std::endl;
            std::cout << "Total modules: " << stats_.total_modules
                     << ", Failed: " << stats_.failed_modules << std::endl;
            if (config_.enable_concurrent_initialization) {
                std::cout << "Parallel groups: " << stats_.parallel_groups << std::endl;
                std::cout << "Average concurrency: " << stats_.average_concurrency << std::endl;
            }
        }

        return success;

    } catch (const std::exception& e) {
        auto end_time = std::chrono::steady_clock::now();
        stats_.total_init_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

        std::cerr << "Initialization failed: " << e.what() << std::endl;

        throw ModuleInitializationException("", e.what());
    }
}

void ModuleManager::shutdown_all() {
    std::lock_guard lock(mutex_);

    std::cout << "Starting module shutdown..." << std::endl;

    // 按照初始化的逆序关闭模块
    auto& registry = ModuleRegistry::instance();

    for (auto it = initialization_order_.rbegin(); it != initialization_order_.rend(); ++it) {
        const auto& type_idx = *it;

        try {
            shutdown_module_internal(type_idx);
        } catch (const std::exception& e) {
            std::cerr << "Error shutting down module: " << e.what() << std::endl;
            // 继续关闭其他模块
        }
    }

    initialization_order_.clear();

    std::cout << "Module shutdown completed." << std::endl;
}

bool ModuleManager::execute_parallel_initialization() {
    auto& registry = ModuleRegistry::instance();
    auto module_groups = registry.create_module_groups();

    if (module_groups.empty()) {
        std::cout << "No modules to initialize." << std::endl;
        return true;
    }

    stats_.parallel_groups = module_groups.size();

    // 计算总模块数
    for (const auto& group : module_groups) {
        stats_.total_modules += group.size();
    }

    std::cout << "Initializing " << stats_.total_modules
              << " modules in " << stats_.parallel_groups << " parallel groups" << std::endl;

    // 使用 std::barrier 进行组间同步
    std::barrier group_barrier(static_cast<std::ptrdiff_t>(module_groups.size()));
    std::atomic<bool> global_success{true};
    std::vector<std::jthread> group_threads;
    std::mutex stats_mutex;

    // 为每个组启动一个线程
    for (const auto& group : module_groups) {
        group_threads.emplace_back([&](const ModuleGroup& current_group) {
            PerformanceStats group_stats;
            bool group_success = initialize_module_group(current_group, group_stats);

            if (!group_success) {
                global_success.store(false);
            }

            // 等待所有组完成
            group_barrier.arrive_and_wait();
        }, group);
    }

    // 等待所有线程完成
    for (auto& thread : group_threads) {
        if (thread.joinable()) {
            thread.join();
        }
    }

    // 计算平均并发度
    if (stats_.parallel_groups > 0) {
        stats_.average_concurrency = static_cast<double>(stats_.parallel_groups);
    }

    return global_success.load();
}

bool ModuleManager::execute_serial_initialization() {
    auto& registry = ModuleRegistry::instance();
    auto module_groups = registry.create_module_groups();

    stats_.parallel_groups = 1; // 串行执行视为单个组

    // 计算总模块数
    for (const auto& group : module_groups) {
        stats_.total_modules += group.size();
    }

    std::cout << "Initializing " << stats_.total_modules << " modules serially" << std::endl;

    // 串行执行每个组
    for (const auto& group : module_groups) {
        PerformanceStats group_stats;
        if (!initialize_module_group(group, group_stats)) {
            // stats_.failed_modules += group_stats.failed_modules;
            // stats_.failed_module_names.insert(
            //     stats_.failed_module_names.end(),
            //     group_stats.failed_module_names.begin(),
            //     group_stats.failed_module_names.end());

            // if (config_.fail_fast) {
            // }
                return false;
        }
    }

    stats_.average_concurrency = 1.0;
    return true;
}

bool ModuleManager::initialize_module_group(const ModuleGroup& group, PerformanceStats& stats) {
    std::cout << "Initializing group " << group.get_group_id()
              << " with " << group.size() << " modules" << std::endl;

    bool group_success = true;

    // 组内模块按优先级串行执行
    for (const auto& [type_idx, registration] : group.get_modules()) {
        try {
            if (!initialize_module_internal(type_idx)) {
                group_success = false;
                // stats.failed_modules++;
                // stats.failed_module_names.push_back(registration->module_info.module_name);
                break;
            }
            initialization_order_.push_back(type_idx);

        } catch (const std::exception& e) {
            group_success = false;
            // stats.failed_modules++;
            // stats.failed_module_names.push_back(registration->module_info.module_name);

            std::cerr << "Module initialization failed: " << e.what() << std::endl;
        }
    }

    return group_success;
}

bool ModuleManager::initialize_all_sequence() {
    auto& registry = ModuleRegistry::instance();
    auto regs = registry.get_all_registrations();

    // 按顺序初始化模块
    size_t initialized_count = 0;
    for (const auto& [type_idx, registration] : regs) {
        try {
            auto it = instances_.find(type_idx);
            if (it == instances_.end()) {
                // 创建实例
                auto instance_holder = registration->factory();
                instances_[type_idx] = std::make_unique<ModuleInstance>(
                    std::move(instance_holder), registration);
                it = instances_.find(type_idx);
            }

            auto& instance = it->second;
            if (instance->state == ModuleState::Uninitialized) {
                instance->state = ModuleState::Initializing;
                instance->init_time = std::chrono::steady_clock::now();

                bool success = registration->initializer(instance->instance.get_raw());
                if (success) {
                    instance->state = ModuleState::Initialized;
                } else {
                    instance->state = ModuleState::Failed;
                }
            }
            ++initialized_count;
        } catch (const std::exception& e) {
            std::cerr << "Failed to initialize module: " << e.what() << std::endl;
        }
    }

    // 记录总初始化时间
    // total_initialization_time_ = std::chrono::duration_cast<std::chrono::milliseconds>(
        // std::chrono::steady_clock::now() - start_time);
    // last_initialization_time_ = std::chrono::steady_clock::now();

    return initialized_count == regs.size();
}

} // namespace modula
