/**
 * @file modula.info.ixx
 * @brief 模块信息管理
 *
 * Manages user-defined module data.
 * Provide a unified module information interface that
 * combines metadata and user data.
 *
 * @version 1.0.0
 */

module;

#include <algorithm>
#include <chrono>
#include <mutex>
#include <shared_mutex>
#include <string>
#include <string_view>
#include <typeindex>
#include <unordered_map>
#include <vector>

#include <iostream>

export module modula.info;

import modula.types;
import modula.metadata;

export namespace modula {
/**
 * @brief User-defined module data
 *
 * Stores module information registered through UserDataRegistry.
 */
struct UserData
{
    std::string name;                      ///< Module name
    std::string version;                   ///< Version string
    std::string description;               ///< Module description
    std::vector<std::string> dependencies; ///< List of dependent module names
};

/**
 * @brief User data registry
 *
 * Manages registration of UserData.
 *
 * @tparam T Module type, must satisfy Module concept
 */
template<Module T>
class UserDataRegistry
{
    static inline UserData user_data_{};
    static inline bool registered_ = false;
    static inline std::chrono::steady_clock::time_point registration_time_{};

public:
    /**
     * @brief Get user data
     * @return Const reference to user data
     */
    [[nodiscard]] static const UserData& data() noexcept {
        return user_data_;
    }

    /**
     * @brief Register user data
     * @param data User-defined module data
     */
    static bool register_data(const UserData& data) {
        if (registered_) {
            return false;
        }
        user_data_ = data;
        registered_ = true;
        registration_time_ = std::chrono::steady_clock::now();
        return true;
    }

    /**
     * @brief Check if data is registered
     * @return true if registered
     */
    [[nodiscard]] static bool is_registered() noexcept {
        return registered_;
    }

    /**
     * @brief Get registration time
     * @return Registration time point
     */
    [[nodiscard]] static std::chrono::steady_clock::time_point registration_time() noexcept {
        return registration_time_;
    }
};

/**
 * @brief Module data interface
 *
 * 提供用户数据和编译期元数据的统一访问接口。
 *
 * @tparam T 模块类型，必须满足Module概念
 */
template<Module T>
class ModuleData
{
public:
    ModuleData() = default;

    ModuleData(const ModuleData&) = default;
    ModuleData(ModuleData&&) = default;
    ModuleData& operator=(const ModuleData&) = default;
    ModuleData& operator=(ModuleData&&) = default;

    /**
     * @brief Check if module information is valid
     * @return true if user data is valid
     */
    [[nodiscard]] bool is_valid() const noexcept {
        return !name().empty();
    }

    /**
     * @brief Equality comparison operator
     */
    bool operator==(const ModuleData& other) const noexcept {
        return name() == other.name()
               && version() == other.version();
    }

    /**
     * @brief Less-than operator for name-based sorting
     */
    bool operator<(const ModuleData& other) const noexcept {
        return name() < other.name();
    }

    /**
     * @brief Get module name (prioritizes user data, falls back to metadata)
     * @return Module name as string_view
     */
    [[nodiscard]] std::string_view name() const noexcept {
        // Use user data if valid and non-empty
        if (UserDataRegistry<T>::is_registered()) {
            const auto& user_data = UserDataRegistry<T>::data();
            if (!user_data.name.empty()) {
                return user_data.name;
            }
        }
        // Fall back to metadata
        return get_metadata<T>().name;
    }

    /**
     * @brief Get module version
     * @return Module version as string_view (prioritizes user data, falls back to metadata)
     */
    [[nodiscard]] std::string_view version() const noexcept {
        // Use user data if valid and non-empty
        if (UserDataRegistry<T>::is_registered()) {
            const auto& user_data = UserDataRegistry<T>::data();
            if (!user_data.version.empty()) {
                return user_data.version;
            }
        }
        // Fall back to metadata
        return get_metadata<T>().version;
    }

    /**
     * @brief Get module description
     * @return String view of the module description
     */
    [[nodiscard]] std::string_view description() const noexcept {
        if (UserDataRegistry<T>::is_registered()) {
            return UserDataRegistry<T>::data().description;
        }
        return {};
    }

    /**
     * @brief Get module dependencies (merges user data and metadata dependencies)
     * @return Vector of dependency names
     */
    [[nodiscard]] std::vector<std::string> dependencies() const {
        std::vector<std::string> result;

        if (UserDataRegistry<T>::is_registered()) {
            result = UserDataRegistry<T>::data().dependencies;
        }

        const auto metadata_deps = get_metadata<T>().dependencies;
        for (const auto& dep : metadata_deps) {
            std::string dep_str{dep};
            if (std::find(result.begin(), result.end(), dep_str) == result.end()) {
                result.push_back(std::move(dep_str));
            }
        }

        return result;
    }

    /**
     * @brief Get module directory path
     * @return String view of the module directory path
     */
    [[nodiscard]] constexpr std::string_view directory() const noexcept {
        return get_metadata<T>().directory;
    }

    /**
     * @brief Get primary file path
     * @return String view of the primary file path
     */
    [[nodiscard]] constexpr std::string_view primary_file() const noexcept {
        return get_metadata<T>().primary_file;
    }

    /**
     * @brief 获取源文件列表（仅来自元数据）
     * @return 源文件路径的span
     */
    [[nodiscard]] constexpr auto source_files() const noexcept {
        return get_metadata<T>().source_files;
    }

    /**
     * @brief 获取构建时间戳（仅来自元数据）
     * @return 构建时间戳的string_view
     */
    [[nodiscard]] constexpr std::string_view build_timestamp() const noexcept {
        return get_metadata<T>().build_timestamp;
    }

    /**
     * @brief Get parallel group number (from metadata)
     * @return Parallel group number
     */
    [[nodiscard]] constexpr int parallel_group() const noexcept {
        return get_metadata<T>().parallel_group;
    }

    /**
     * @brief Get parallel priority (from metadata)
     * @return Parallel initialization priority
     */
    [[nodiscard]] constexpr int parallel_priority() const noexcept {
        return get_metadata<T>().parallel_priority;
    }
};

/**
 * @brief Non-template module information class
 *
 * 提供类型擦除的模块信息存储，用于运行时访问。
 * 存储从 ModuleData<T> API 中获取的所有值作为成员变量。
 */
class ModuleInfo
{
private:
    std::string name_;                         ///< Module name
    std::string version_;                      ///< Module version
    std::string description_;                  ///< Module description
    std::string directory_;                    ///< Module directory path
    std::string primary_file_;                 ///< Primary file path
    std::string build_timestamp_;              ///< Build timestamp
    std::vector<std::string> dependencies_;    ///< Module dependencies
    std::vector<std::string> source_files_;    ///< Source files list
    int parallel_group_;                       ///< Parallel group identifier
    int parallel_priority_ = priority::NORMAL; ///< Metadata parallel priority

public:
    /**
     * @brief Default constructor
     */
    ModuleInfo() = default;

    /**
     * @brief Copy constructor
     */
    ModuleInfo(const ModuleInfo&) = default;

    /**
     * @brief Move constructor
     */
    ModuleInfo(ModuleInfo&&) = default;

    /**
     * @brief Copy assignment operator
     */
    ModuleInfo& operator=(const ModuleInfo&) = default;

    /**
     * @brief Move assignment operator
     */
    ModuleInfo& operator=(ModuleInfo&&) = default;

    // Getter functions

    /**
     * @brief Get the module name
     * @return Const reference to the module name
     */
    [[nodiscard]] const std::string& name() const noexcept {
        return name_;
    }

    /**
     * @brief Get the module version
     * @return Const reference to the module version
     */
    [[nodiscard]] const std::string& version() const noexcept {
        return version_;
    }

    /**
     * @brief Get the module description
     * @return Const reference to the module description
     */
    [[nodiscard]] const std::string& description() const noexcept {
        return description_;
    }

    /**
     * @brief Get the module directory path
     * @return Const reference to the module directory path
     */
    [[nodiscard]] const std::string& directory() const noexcept {
        return directory_;
    }

    /**
     * @brief Get the primary file path
     * @return Const reference to the primary file path
     */
    [[nodiscard]] const std::string& primary_file() const noexcept {
        return primary_file_;
    }

    /**
     * @brief Get the build timestamp
     * @return Const reference to the build timestamp
     */
    [[nodiscard]] const std::string& build_timestamp() const noexcept {
        return build_timestamp_;
    }

    /**
     * @brief Get the module dependencies
     * @return Const reference to the module dependencies vector
     */
    [[nodiscard]] const std::vector<std::string>& dependencies() const noexcept {
        return dependencies_;
    }

    /**
     * @brief Get the source files list
     * @return Const reference to the source files vector
     */
    [[nodiscard]] const std::vector<std::string>& source_files() const noexcept {
        return source_files_;
    }

    /**
     * @brief Get the parallel group identifier
     * @return The parallel group identifier
     */
    [[nodiscard]] int parallel_group() const noexcept {
        return parallel_group_;
    }

    /**
     * @brief Get the parallel priority
     * @return The parallel priority value
     */
    [[nodiscard]] int parallel_priority() const noexcept {
        return parallel_priority_;
    }

    // Setter functions

    /**
     * @brief Set the module name
     * @param name The new module name
     */
    void set_name(const std::string& name) {
        name_ = name;
    }

    /**
     * @brief Set the module name (move version)
     * @param name The new module name
     */
    void set_name(std::string&& name) {
        name_ = std::move(name);
    }

    /**
     * @brief Set the module version
     * @param version The new module version
     */
    void set_version(const std::string& version) {
        version_ = version;
    }

    /**
     * @brief Set the module version (move version)
     * @param version The new module version
     */
    void set_version(std::string&& version) {
        version_ = std::move(version);
    }

    /**
     * @brief Set the module description
     * @param description The new module description
     */
    void set_description(const std::string& description) {
        description_ = description;
    }

    /**
     * @brief Set the module description (move version)
     * @param description The new module description
     */
    void set_description(std::string&& description) {
        description_ = std::move(description);
    }

    /**
     * @brief Set the module directory path
     * @param directory The new module directory path
     */
    void set_directory(const std::string& directory) {
        directory_ = directory;
    }

    /**
     * @brief Set the module directory path (move version)
     * @param directory The new module directory path
     */
    void set_directory(std::string&& directory) {
        directory_ = std::move(directory);
    }

    /**
     * @brief Set the primary file path
     * @param primary_file The new primary file path
     */
    void set_primary_file(const std::string& primary_file) {
        primary_file_ = primary_file;
    }

    /**
     * @brief Set the primary file path (move version)
     * @param primary_file The new primary file path
     */
    void set_primary_file(std::string&& primary_file) {
        primary_file_ = std::move(primary_file);
    }

    /**
     * @brief Set the build timestamp
     * @param build_timestamp The new build timestamp
     */
    void set_build_timestamp(const std::string& build_timestamp) {
        build_timestamp_ = build_timestamp;
    }

    /**
     * @brief Set the build timestamp (move version)
     * @param build_timestamp The new build timestamp
     */
    void set_build_timestamp(std::string&& build_timestamp) {
        build_timestamp_ = std::move(build_timestamp);
    }

    /**
     * @brief Set the module dependencies
     * @param dependencies The new module dependencies vector
     */
    void set_dependencies(const std::vector<std::string>& dependencies) {
        dependencies_ = dependencies;
    }

    /**
     * @brief Set the module dependencies (move version)
     * @param dependencies The new module dependencies vector
     */
    void set_dependencies(std::vector<std::string>&& dependencies) {
        dependencies_ = std::move(dependencies);
    }

    /**
     * @brief Set the source files list
     * @param source_files The new source files vector
     */
    void set_source_files(const std::vector<std::string>& source_files) {
        source_files_ = source_files;
    }

    /**
     * @brief Set the source files list (move version)
     * @param source_files The new source files vector
     */
    void set_source_files(std::vector<std::string>&& source_files) {
        source_files_ = std::move(source_files);
    }

    /**
     * @brief Set the parallel group identifier
     * @param parallel_group The new parallel group identifier
     */
    void set_parallel_group(int parallel_group) noexcept {
        parallel_group_ = parallel_group;
    }

    /**
     * @brief Set the parallel priority
     * @param parallel_priority The new parallel priority value
     */
    void set_parallel_priority(int parallel_priority) noexcept {
        parallel_priority_ = parallel_priority;
    }

    /**
     * @brief Check if module information is valid
     * @return true if module name is not empty
     */
    [[nodiscard]] bool is_valid() const noexcept {
        return !name_.empty();
    }

    /**
     * @brief Equality comparison operator
     */
    bool operator==(const ModuleInfo& other) const noexcept {
        return name_ == other.name_
               && version_ == other.version_;
    }

    /**
     * @brief Less-than operator for name-based sorting
     */
    bool operator<(const ModuleInfo& other) const noexcept {
        return name_ < other.name_;
    }
};

/**
 * @brief Create type-erased ModuleInfo from template ModuleData
 *
 * 提供从模板化数据到非模板化存储的转换功能。
 * 实现类型擦除，将 ModuleData<T> 的所有值提取到 ModuleInfo 对象中。
 *
 * @tparam T Module type, must satisfy Module concept
 * @return ModuleInfo instance with all values extracted from ModuleData<T>
 */
template<Module T>
[[nodiscard]] ModuleInfo create_module_info() {
    ModuleData<T> module_data;

    ModuleInfo info;
    info.set_name(std::string(module_data.name()));
    info.set_version(std::string(module_data.version()));
    info.set_description(std::string(module_data.description()));
    info.set_directory(std::string(module_data.directory()));
    info.set_primary_file(std::string(module_data.primary_file()));
    info.set_build_timestamp(std::string(module_data.build_timestamp()));
    info.set_dependencies(module_data.dependencies());

    // Convert source_files span to vector
    const auto source_files_span = module_data.source_files();
    std::vector<std::string> source_files_vec;
    source_files_vec.reserve(source_files_span.size());
    for (const auto& file : source_files_span) {
        source_files_vec.emplace_back(file);
    }
    info.set_source_files(std::move(source_files_vec));

    info.set_parallel_group(module_data.parallel_group());
    info.set_parallel_priority(module_data.parallel_priority());

    return info;
}
} // namespace modula
