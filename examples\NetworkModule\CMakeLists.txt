# NetworkModule CMakeLists.txt
# 网络模块 - 依赖CoreModule
# 用于并发初始化性能测试

add_library(NetworkModule)

set_target_properties(NetworkModule PROPERTIES
    VERSION 1.0.0
)

target_sources(NetworkModule
    PUBLIC
        FILE_SET CXX_MODULES FILES
            NetworkModule.module.ixx
    PRIVATE
        network_module_registration.cpp
)

target_link_libraries(NetworkModule PUBLIC modula CoreModule)
declare_module(NetworkModule)

set_target_properties(NetworkModule PROPERTIES
    CXX_STANDARD 23
    CXX_STANDARD_REQUIRED ON
    CXX_EXTENSIONS OFF
)

if(CMAKE_CXX_COMPILER_ID STREQUAL "MSVC")
    target_compile_options(NetworkModule PRIVATE
        /W4 /permissive- /utf-8 /std:c++latest /experimental:module /bigobj /Zc:__cplusplus /wd4819
    )
    target_compile_definitions(NetworkModule PRIVATE
        UNICODE _UNICODE _CRT_SECURE_NO_WARNINGS NOMINMAX
    )
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
    target_compile_options(NetworkModule PRIVATE
        -Wall -Wextra -Wpedantic -std=c++23 -fdiagnostics-color=always -fmodules-ts
        -finput-charset=UTF-8 -fexec-charset=UTF-8 -fwide-exec-charset=UTF-32LE
    )
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
    target_compile_options(NetworkModule PRIVATE
        -Wall -Wextra -Wpedantic -std=c++23 -fdiagnostics-color=always -fmodules
        -finput-charset=UTF-8 -fexec-charset=UTF-8
    )
endif()

target_compile_definitions(NetworkModule PRIVATE
    MODULA_CXX_STANDARD=23
    MODULA_MODULES_AVAILABLE=1
    NETWORKMODULE_VERSION_MAJOR=1
    NETWORKMODULE_VERSION_MINOR=0
    NETWORKMODULE_VERSION_PATCH=0
)

if(DEFINED CMAKE_CXX_MODULE_DIRECTORY)
    set_target_properties(NetworkModule PROPERTIES
        CXX_MODULE_DIRECTORY "${CMAKE_CXX_MODULE_DIRECTORY}/NetworkModule"
    )
endif()

set_target_properties(NetworkModule PROPERTIES
    EXPORT_NAME NetworkModule
    OUTPUT_NAME NetworkModule
)
