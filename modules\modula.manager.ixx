/**
 * @file modula.manager.ixx
 * @brief 模块管理器
 *
 * 提供模块生命周期管理、依赖解析和运行时调度
 *
 * **主要功能：**
 * 1. 自动依赖解析和初始化顺序确定
 * 2. 线程安全的模块生命周期管理
 * 3. 异常安全的回滚机制和错误恢复
 * 4. 性能监控和资源使用统计
 * 5. 热重载和动态模块管理
 * 6. 详细的状态跟踪和诊断报告
 *
 * @version 1.0.0
 */

module;

#include <memory>
#include <mutex>
#include <shared_mutex>
#include <string>
#include <typeindex>
#include <unordered_map>
#include <unordered_set>
#include <chrono>
#include <functional>
#include <iostream>
#include <vector>
#include <thread>
#include <atomic>
#include <barrier>
#include <latch>
#include <future>
#include <exception>

export module modula.manager;

import modula.types;
import modula.metadata;
import modula.info;
import modula.registry;

export namespace modula {

/**
  * @brief 模块管理器
  *
  * 提供完整的模块生命周期管理，包括：
  * - 自动依赖解析和初始化顺序确定
  * - 线程安全的模块操作和并发控制
  * - 详细的状态跟踪和性能监控
  * - 异常安全的回滚机制和错误恢复
  * - 热重载和动态模块管理
  * - 资源使用监控和内存管理
  * - 智能缓存和性能优化
  */
class ModuleManager
{
public:
    /**
     * @brief 模块管理器配置结构
     *
     * 包含模块管理器的所有配置选项，支持运行时动态调整
     */
    struct ManagerConfig
    {
        bool enable_hot_reload = false;               ///< 启用热重载功能
        bool enable_lazy_loading = false;             ///< 启用延迟加载
        bool enable_performance_monitoring = true;    ///< 启用性能监控
        bool enable_memory_monitoring = true;         ///< 启用内存监控
        bool enable_concurrent_initialization = true; ///< 启用并发初始化
        int default_timeout_ms = 10000;                ///< 默认超时时间（毫秒）
        int memory_limit_mb = 1024;                   ///< 内存限制（MB）
        std::size_t max_threads = std::thread::hardware_concurrency(); ///< 最大并发线程数
        std::string log_level = "INFO";               ///< 日志级别

        /**
         * @brief 验证配置的有效性
         *
         * @return bool 如果配置有效返回true，否则返回false
         */
        [[nodiscard]] bool is_valid() const noexcept {
            return default_timeout_ms > 0 &&
                   memory_limit_mb > 0 &&
                   !log_level.empty() &&
                   max_threads > 0;
        }
    };

    /**
     * @brief 性能统计结构
     */
    struct PerformanceStats
    {
        std::chrono::milliseconds total_init_time{0};        ///< 总初始化时间
        size_t threads_used{0};                              ///< 使用的线程数
        size_t total_modules{0};                       ///< 总模块数
        size_t failed_modules{0};                            ///< 失败模块数
        std::size_t parallel_groups = 0;                 ///< 并行组数
        double average_concurrency = 0.0;               ///< 平均并发度
    };

    /**
      * @brief 模块实例信息 - 完整的生命周期和性能跟踪
      *
      * 包含模块实例的完整状态信息、性能统计和配置选项
      * 支持详细的时间跟踪、内存监控和错误统计
      */
    struct ModuleInstance
    {
        ModuleInstanceHolder instance;                  ///< 模块实例持有者
        const ModuleRegistration* registration;         ///< 注册信息指针
        ModuleState state = ModuleState::Uninitialized; ///< 当前状态

        // 时间统计 - 详细的生命周期时间跟踪
        std::chrono::steady_clock::time_point init_start_time;  ///< 初始化开始时间
        std::chrono::steady_clock::time_point init_time;        ///< 初始化完成时间
        std::chrono::steady_clock::time_point last_access_time; ///< 最后访问时间
        std::chrono::steady_clock::time_point shutdown_time;    ///< 关闭时间
        std::chrono::milliseconds total_init_duration{0};       ///< 总初始化耗时

        // 性能统计 - 运行时性能监控
        std::atomic<size_t> access_count{0};            ///< 访问次数（线程安全）
        std::atomic<size_t> memory_usage_bytes{0};      ///< 当前内存使用（字节）
        std::atomic<size_t> peak_memory_usage_bytes{0}; ///< 峰值内存使用（字节）

        // 错误统计 - 故障跟踪和诊断
        std::atomic<size_t> initialization_attempts{0}; ///< 初始化尝试次数
        std::atomic<size_t> failure_count{0};           ///< 失败次数
        std::string last_error_message;                 ///< 最后的错误消息

        // 配置选项 - 模块特定的运行时配置
        bool hot_reload_enabled = false; ///< 热重载启用标志
        bool lazy_loaded = false;        ///< 延迟加载标志
        int timeout_ms = 5000;           ///< 操作超时时间

        /**
          * @brief 构造函数
          *
          * @param inst 模块实例持有者
          * @param reg 模块注册信息指针
          */
        ModuleInstance(ModuleInstanceHolder inst, const ModuleRegistration* reg)
            : instance(std::move(inst))
            , registration(reg) {
            const auto now = std::chrono::steady_clock::now();
            init_start_time = now;
            last_access_time = now;
        }

        /**
          * @brief 更新访问统计 - 线程安全的统计更新
          *
          * 原子操作更新访问计数和最后访问时间
          */
        void update_access_stats() noexcept {
            last_access_time = std::chrono::steady_clock::now();
            access_count.fetch_add(1, std::memory_order_relaxed);
        }

        /**
          * @brief 获取模块运行时长
          *
          * 计算从初始化完成到当前时刻的运行时长
          *
          * @return std::chrono::milliseconds 运行时长，如果未初始化则返回零
          */
        [[nodiscard]] auto get_uptime() const noexcept {
            if (state != ModuleState::Initialized) {
                return std::chrono::milliseconds{0};
            }
            return std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::steady_clock::now() - init_time);
        }

        /**
          * @brief 检查模块是否处于空闲状态
          *
          * 判断模块是否超过指定时间未被访问
          *
          * @param idle_threshold 空闲时间阈值
          * @return bool 如果超过阈值时间未访问返回true
          */
        [[nodiscard]] bool is_idle(std::chrono::milliseconds idle_threshold) const noexcept {
            const auto idle_time = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::steady_clock::now() - last_access_time);
            return idle_time > idle_threshold;
        }

        /**
          * @brief 更新内存使用统计
          *
          * @param current_usage 当前内存使用量（字节）
          */
        void update_memory_usage(size_t current_usage) noexcept {
            memory_usage_bytes.store(current_usage, std::memory_order_relaxed);

            // 更新峰值内存使用
            size_t current_peak = peak_memory_usage_bytes.load(std::memory_order_relaxed);
            while (current_usage > current_peak) {
                if (peak_memory_usage_bytes.compare_exchange_weak(
                        current_peak, current_usage, std::memory_order_relaxed)) {
                    break;
                }
            }
        }

        /**
          * @brief 记录初始化尝试
          */
        void record_initialization_attempt() noexcept {
            initialization_attempts.fetch_add(1, std::memory_order_relaxed);
        }

        /**
          * @brief 记录失败
          *
          * @param error_message 错误消息
          */
        void record_failure(const std::string& error_message) noexcept {
            failure_count.fetch_add(1, std::memory_order_relaxed);
            last_error_message = error_message;
        }
    };

    /**
     * @brief 获取单例实例
     */
    static ModuleManager& instance() {
        static ModuleManager instance;
        return instance;
    }

    /**
      * @brief 构造函数
      *
      * 初始化模块管理器，设置默认配置和性能监控
      * 提供异常安全的初始化过程
     * @param config 管理器配置
      */
    explicit ModuleManager(const ManagerConfig& config = {})
        : config_(config) {
        // 初始化性能监控
        start_time_ = std::chrono::steady_clock::now();

        // 初始化性能统计
        stats_ = PerformanceStats{};
    }

    /**
      * @brief 析构函数 - 异常安全的自动关闭所有模块
      *
      * 确保所有模块在管理器销毁时正确关闭，防止资源泄漏
      */
    ~ModuleManager() noexcept {
        try {
            shutdown_all();
        } catch (...) {
            // 析构函数中不抛出异常
        }
    }

    // 禁用拷贝和移动语义
    ModuleManager(const ModuleManager&) = delete;
    ModuleManager& operator=(const ModuleManager&) = delete;
    ModuleManager(ModuleManager&&) = delete;
    ModuleManager& operator=(ModuleManager&&) = delete;

    /**
      * @brief 设置管理器配置
      *
      * 线程安全的配置更新，支持运行时动态调整
      *
      * @param config 新的配置，必须有效
      * @throws std::invalid_argument 如果配置无效
      */
    void set_config(const ManagerConfig& config) {
        if (!config.is_valid()) {
            throw ModuleConfigurationException("Invalid manager configuration provided");
        }

        std::lock_guard<std::recursive_mutex> lock(mutex_);
        config_ = config;
    }

    /**
      * @brief 获取管理器配置
      *
      * @return const ManagerConfig& 当前配置的常量引用
      */
    [[nodiscard]] const ManagerConfig& get_config() const noexcept {
        std::lock_guard lock(mutex_);
        return config_;
    }
    
    /**
     * @brief 获取性能统计信息
     *
     * @return const PerformanceStats& 性能统计的常量引用
     */
    [[nodiscard]] const PerformanceStats& get_performance_stats() const noexcept {
        std::lock_guard lock(mutex_);
        return stats_;
    }

    /**
     * @brief 重置性能统计信息
     */
    void reset_performance_stats() {
        std::lock_guard lock(mutex_);
        stats_ = PerformanceStats{};
        start_time_ = std::chrono::steady_clock::now();
    }

    /**
     * @brief 重新加载模块
     *
     * 先关闭模块，然后重新初始化
     *
     * @tparam T 模块类型，必须满足Module概念
     * @return 如果重载成功返回true
     */
    template<Module T>
    bool reload_module() {
        std::lock_guard lock(mutex_);
        auto type_idx = std::type_index(typeid(T));

        // 先关闭模块
        if (!shutdown_module_internal(type_idx)) {
            return false;
        }

        // 重新初始化
        return initialize_module_internal(type_idx);
    }

    /**
      * @brief 初始化指定模块

      * @tparam T 模块类型，必须满足Module概念
      * @return 如果初始化成功返回true
      */
    template<Module T>
    bool initialize_module() {
        std::lock_guard<std::recursive_mutex> lock(mutex_);
        auto type_idx = std::type_index(typeid(T));
        return initialize_module_internal(type_idx);
    }

    /**
      * @brief 关闭指定模块
      * @tparam T 模块类型，必须满足Module概念
      * @return 如果关闭成功返回true
      */
    template<Module T>
    bool shutdown_module() {
        std::lock_guard<std::recursive_mutex> lock(mutex_);
        auto type_idx = std::type_index(typeid(T));
        return shutdown_module_internal(type_idx);
    }

    /**
     * @brief 检查模块是否已初始化
     * @tparam T 模块类型，必须满足Module概念
     * @return 如果模块已成功初始化返回true
     */
    template<Module T>
    [[nodiscard]] bool is_module_initialized() const {
        std::lock_guard<std::recursive_mutex> lock(mutex_);

        auto type_idx = std::type_index(typeid(T));

        auto it = instances_.find(type_idx);
        return it != instances_.end() && it->second->state == ModuleState::Initialized;
    }

    /**
      * @brief 获取模块状态
      * @tparam T 模块类型，必须满足Module概念
      * @return ModuleState 模块当前状态
      */
    template<Module T>
    [[nodiscard]] ModuleState get_module_state() const {
        std::lock_guard<std::recursive_mutex> lock(mutex_);

        auto type_idx = std::type_index(typeid(T));

        auto it = instances_.find(type_idx);
        return it != instances_.end() ? it->second->state : ModuleState::Uninitialized;
    }

    /**
      * @brief 获取模块实例
      * @tparam T 模块类型，必须与注册时的类型匹配
      * @return T* 模块实例指针，如果不存在、未初始化或类型不匹配则返回nullptr
      */
    template<typename T>
    [[nodiscard]] T* get_module() const {
        std::lock_guard<std::recursive_mutex> lock(mutex_);

        auto type_idx = std::type_index(typeid(T));

        const auto it = instances_.find(type_idx);
        if (it != instances_.end() && it->second->state == ModuleState::Initialized) {
            // 更新访问统计
            it->second->update_access_stats();
            return it->second->instance.get_as<T>();
        }
        return nullptr;
    }

    /**
     * @brief 清空所有模块实例
     */
    void clear() {
        std::lock_guard lock(mutex_);
        instances_.clear();
    }

    /**
      * @brief 初始化所有注册的模块
      *
      * 智能选择初始化策略：
      * - 当启用并发初始化且有编译时类型信息时，使用高性能并行初始化
      * - 根据模块数量和系统资源自动优化并发策略
      * - 提供详细的性能监控和统计信息
      * - 异常安全的初始化过程和自动错误处理
      * - 向后兼容传统的串行初始化
      *
      * **并行初始化特性：**
      * - 基于编译时依赖分析的层级并行执行
      * - 使用现代C++20/23特性（std::jthread, std::barrier）
      * - 智能线程池管理和资源控制
      * - 实时性能监控和统计收集
      * - 异常聚合和详细错误报告
      *
      * @return bool 如果所有模块都成功初始化返回true，否则返回false
      * @throws ParallelInitializationException 如果并行初始化过程中发生错误
      * @throws ModuleManagerException 如果初始化过程中发生严重错误
      * @throws ModuleManagerCircularDependencyException 如果检测到循环依赖
      *
      * @note 初始化策略由 ManagerConfig::enable_concurrent_initialization 控制
      * @note 性能统计信息可通过 get_performance_stats() 获取
      */
    bool initialize_all();

    void shutdown_all();

    bool execute_parallel_initialization();

    bool execute_serial_initialization();

    bool initialize_module_group(const ModuleGroup& group, PerformanceStats& stats);

    /**
     * @brief 根据模块注册信息初始化模块
     *
     * 用于运行时并行初始化器调用的公有方法。
     *
     * @param registration 模块注册信息
     * @return 如果模块初始化成功返回true
     */
    bool initialize_module_by_registration(const ModuleRegistration& registration);

    /**
      * @brief 关闭所有模块
      *
      * 线程安全的批量关闭操作，按照依赖关系的逆序关闭所有模块
      * 确保依赖关系正确处理，避免悬空引用
      */
    bool shutdown_alll_sequence() noexcept {
        auto& registry = ModuleRegistry::instance();
        auto regs = registry.get_all_registrations();

        bool all_success = true;

        // 逆序关闭模块
        for (auto it = regs.rbegin(); it != regs.rend(); ++it) {
            const auto& [type_key, registration] = *it;

            try {
                auto inst_it = instances_.find(type_key);
                if (inst_it != instances_.end() && inst_it->second->state == ModuleState::Initialized) {
                    auto& instance = inst_it->second;
                    instance->state = ModuleState::Shutting_down;
                    instance->shutdown_time = std::chrono::steady_clock::now();

                    registration->shutdown(instance->instance.get_raw());
                    instance->state = ModuleState::Uninitialized;
                }
            } catch (const std::exception&) {
                all_success = false;
            }
        }

        return all_success;
    }

private:
    mutable std::recursive_mutex mutex_;
    std::unordered_map<std::type_index, std::unique_ptr<ModuleInstance>> instances_;
    std::vector<std::type_index> initialization_order_;        ///< 初始化顺序（用于关闭）

    // 配置和监控相关成员
    ManagerConfig config_;                                    ///< 管理器配置
    PerformanceStats stats_;                                  ///< 性能统计
    std::chrono::steady_clock::time_point start_time_;
    std::chrono::steady_clock::time_point last_initialization_time_;
    std::chrono::milliseconds total_initialization_time_{0};

    bool initialize_all_sequence();

    /**
     * @brief 内部初始化模块实现（无锁）
     */
    bool initialize_module_internal(const std::type_index& type_idx) {
        // 获取注册信息
        auto& registry = ModuleRegistry::instance();
        auto* registration = registry.get_registration_by_type_index(type_idx);
        if (!registration) {
            return false;
        }

        // 检查模块是否已存在
        auto it = instances_.find(type_idx);
        if (it != instances_.end()) {
            if (it->second->state == ModuleState::Initialized) {
                return true; // 已经初始化
            }
            if (it->second->state == ModuleState::Failed) {
                return false; // 之前初始化失败
            }
        }

        // 创建模块实例（如果还没有）
        if (it == instances_.end()) {
            auto instance_holder = registration->factory();
            instances_[type_idx] = std::make_unique<ModuleInstance>(
                std::move(instance_holder), registration);
            it = instances_.find(type_idx);
        }

        auto& instance = it->second;

        // 性能监控：记录初始化开始时间
        auto init_start_time = std::chrono::steady_clock::now();

        // 设置状态为正在初始化
        instance->state = ModuleState::Initializing;
        instance->init_time = init_start_time;

        // 执行初始化（异常安全）
        bool success = false;
        std::string error_message;

        try {
            success = registration->initializer(instance->instance.get_raw());
        } catch (const std::exception& e) {
            error_message = e.what();
        } catch (...) {
            error_message = "Unknown exception during module initialization";
        }

        // 性能监控：计算初始化时间
        auto init_end_time = std::chrono::steady_clock::now();
        auto init_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
            init_end_time - init_start_time);

        if (success) {
            instance->state = ModuleState::Initialized;

            // 更新性能统计（如果启用）
            if (config_.enable_performance_monitoring) {
                stats_.total_init_time += init_duration;
            }
        } else {
            instance->state = ModuleState::Failed;

            // 更新失败统计
            if (config_.enable_performance_monitoring) {
                stats_.failed_modules++;
            }

            // 如果配置要求抛出异常，则抛出详细的异常信息
            if (!error_message.empty()) {
                // throw ModuleInitializationException(module_name);
                // 注意：这里不抛出异常，而是记录错误，保持向后兼容性
                // 可以通过配置选项控制是否抛出异常
            }
        }

        return success;
    }

    /**
      * @brief 内部关闭模块实现（无锁）
      */
    bool shutdown_module_internal(const std::type_index& type_idx) {

        auto it = instances_.find(type_idx);
        if (it == instances_.end() || it->second->state != ModuleState::Initialized) {
            return true; // 模块不存在或未初始化
        }

        auto& instance = it->second;

        // 设置状态为正在关闭
        instance->state = ModuleState::Shutting_down;
        instance->shutdown_time = std::chrono::steady_clock::now();

        // 执行关闭
        if (instance->registration && instance->registration->shutdown) {
            instance->registration->shutdown(instance->instance.get_raw());
        }

        instance->state = ModuleState::Uninitialized;
        return true;
    }
};
} // namespace modula
