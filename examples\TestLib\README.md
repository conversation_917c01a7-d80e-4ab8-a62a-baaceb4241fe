# TestLib - Traditional C++ Utility Library

TestLib是一个传统的C++静态库，专门设计用于演示C++20模块与传统C++库的互操作性。它提供了一系列实用的功能模块，包括数学计算、字符串处理、数据验证、性能测量和统计分析。

## 特性

### 数学功能 (`testlib::math`)
- `factorial(n)` - 计算阶乘
- `gcd(a, b)` - 计算最大公约数
- `lcm(a, b)` - 计算最小公倍数
- `is_prime(n)` - 素数检测
- `power(base, exponent)` - 幂运算
- `sqrt_newton(x, precision)` - 牛顿法求平方根

### 字符串处理 (`testlib::string_utils`)
- `to_upper(str)` / `to_lower(str)` - 大小写转换
- `trim(str)` - 去除首尾空白字符
- `split(str, delimiter)` - 字符串分割
- `join(strings, delimiter)` - 字符串连接
- `starts_with(str, prefix)` / `ends_with(str, suffix)` - 前缀/后缀检查
- `replace_all(str, from, to)` - 字符串替换

### 数据验证 (`testlib::validation`)
- `is_valid_email(email)` - 邮箱地址验证
- `is_numeric(str)` - 数字字符串检查
- `is_valid_ipv4(ip)` - IPv4地址验证
- `password_strength(password)` - 密码强度评估

### 性能测量 (`testlib::performance`)
- `Timer` 类 - 高精度计时器
- `format_duration(ms)` - 时间格式化
- `calculate_ops_per_second(ops, duration)` - 计算操作速率

### 统计分析 (`testlib::data_structures`)
- `Statistics` 类 - 统计数据分析
  - `mean()` - 平均值
  - `median()` - 中位数
  - `standard_deviation()` - 标准差
  - `min()` / `max()` - 最值

## 使用方式

### 在传统C++代码中使用

```cpp
#include "test_lib.h"

int main() {
    // 数学计算
    auto result = testlib::math::factorial(5);
    
    // 字符串处理
    std::string text = "  Hello World  ";
    auto trimmed = testlib::string_utils::trim(text);
    
    // 性能测量
    testlib::performance::Timer timer;
    timer.start();
    // ... 执行一些操作
    auto elapsed = timer.stop();
    
    return 0;
}
```

### 在C++20模块中使用

```cpp
module;

#include "test_lib.h"  // 在全局模块片段中包含

export module MyModule;

export class MyModule {
public:
    std::string demonstrate_testlib() {
        // 在模块中使用TestLib功能
        auto factorial_result = testlib::math::factorial(6);
        auto trimmed = testlib::string_utils::trim("  test  ");
        
        return "Factorial: " + std::to_string(factorial_result) + 
               ", Trimmed: '" + trimmed + "'";
    }
};
```

## 构建配置

TestLib使用CMake构建系统，配置为静态库：

```cmake
# 创建静态库
add_library(TestLib STATIC)

# 添加源文件
target_sources(TestLib
    PRIVATE test_lib.cpp
    PUBLIC test_lib.h
)

# 链接TestLib到你的目标
target_link_libraries(YourTarget PUBLIC TestLib)
```

## 与ModularFramework集成

TestLib已经集成到ModularFramework的示例项目中：

1. **TestModuleA** - 演示基本的TestLib功能集成
2. **TestModuleB** - 演示高级的TestLib功能集成

### TestModuleA集成示例

TestModuleA展示了如何在C++20模块中使用TestLib的基本功能：

```cpp
// 在TestModuleA.module.ixx中
module;
#include "test_lib.h"
export module TestModuleA;

export class TestModuleA {
public:
    std::string demonstrate_testlib_integration() const {
        // 使用数学功能
        auto factorial_result = testlib::math::factorial(5);
        
        // 使用字符串处理
        auto trimmed = testlib::string_utils::trim("  Hello  ");
        
        // 使用验证功能
        bool is_valid = testlib::validation::is_valid_email("<EMAIL>");
        
        // 返回演示结果
        return "Math: " + std::to_string(factorial_result) + 
               ", String: '" + trimmed + "'" +
               ", Email valid: " + (is_valid ? "true" : "false");
    }
};
```

### TestModuleB集成示例

TestModuleB展示了更高级的TestLib功能使用：

```cpp
// 在testmoduleb_module.h中
#include "test_lib.h"

class TestModuleB {
public:
    std::string demonstrate_advanced_testlib_features() const {
        // 统计分析
        testlib::data_structures::Statistics stats;
        // ... 添加数据和分析
        
        // 性能基准测试
        testlib::performance::Timer timer;
        // ... 执行性能测试
        
        return "Advanced TestLib features demonstrated";
    }
};
```

## 设计原则

1. **命名空间隔离** - 所有功能都在`testlib`命名空间下，避免命名冲突
2. **头文件兼容** - 设计为既可以header-only使用，也可以作为静态库链接
3. **现代C++** - 使用C++20/23特性，但保持与传统代码的兼容性
4. **模块友好** - 可以在全局模块片段中包含，与C++20模块无缝集成
5. **性能优化** - 提供高效的算法实现和性能测量工具

## 互操作性验证

项目中的`comprehensive_module_verification.cpp`包含了完整的互操作性测试：

1. **直接功能测试** - 验证TestLib的所有功能模块
2. **模块集成测试** - 验证C++20模块中使用TestLib的能力
3. **性能基准测试** - 测量TestLib功能的性能表现
4. **统计分析验证** - 验证统计功能的正确性

运行验证程序可以确保：
- TestLib功能正常工作
- C++20模块可以正确使用传统库
- 性能表现符合预期
- 所有集成点都正常工作

这个设计展示了如何在现代C++项目中平滑地集成传统库，为渐进式模块化提供了实用的解决方案。
