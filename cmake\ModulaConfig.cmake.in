# @file ModulaConfig.cmake.in
# @brief Modula Framework Package Configuration
# @version 1.0.0
#
# This file provides the CMake package configuration for finding and using
# the Modula Framework in external projects.

@PACKAGE_INIT@

# ==============================================================================
# Requirements Validation
# ==============================================================================

if(CMAKE_CXX_STANDARD AND CMAKE_CXX_STANDARD LESS 20)
    set_and_check(Modula_NOT_FOUND_MESSAGE
        "Modula Framework requires C++20 or later. Current: ${CMAKE_CXX_STANDARD}")
    set(Modula_FOUND FALSE)
    return()
endif()

# ==============================================================================
# Package Configuration
# ==============================================================================

set_and_check(Modula_CMAKE_DIR "@PACKAGE_CMAKE_INSTALL_LIBDIR@/cmake/Modula")
include("${Modula_CMAKE_DIR}/modules/ModulaDeclaration.cmake")
include("${CMAKE_CURRENT_LIST_DIR}/ModulaTargets.cmake")

# ==============================================================================
# Component Management
# ==============================================================================

set(Modula_KNOWN_COMPONENTS modula)

if(Modula_FIND_COMPONENTS)
    foreach(component ${Modula_FIND_COMPONENTS})
        if(NOT component IN_LIST Modula_KNOWN_COMPONENTS)
            set(Modula_FOUND FALSE)
            set(Modula_NOT_FOUND_MESSAGE "Unknown component: ${component}")
            return()
        endif()

        if(TARGET Modula::${component})
            set(Modula_${component}_FOUND TRUE)
        else()
            set(Modula_${component}_FOUND FALSE)
            if(Modula_FIND_REQUIRED_${component})
                set(Modula_FOUND FALSE)
                set(Modula_NOT_FOUND_MESSAGE "Required component not found: ${component}")
                return()
            endif()
        endif()
    endforeach()
else()
    if(TARGET Modula::modula)
        set(Modula_modula_FOUND TRUE)
    else()
        set(Modula_modula_FOUND FALSE)
    endif()
endif()

# ==============================================================================
# Version Information
# ==============================================================================

set(Modula_VERSION @PROJECT_VERSION@)
set(Modula_VERSION_MAJOR @PROJECT_VERSION_MAJOR@)
set(Modula_VERSION_MINOR @PROJECT_VERSION_MINOR@)
set(Modula_VERSION_PATCH @PROJECT_VERSION_PATCH@)

# ==============================================================================
# Validation and Setup
# ==============================================================================

check_required_components(Modula)

if(NOT Modula_FIND_QUIETLY)
    message(STATUS "Found Modula Framework ${Modula_VERSION}")
    if(Modula_FIND_COMPONENTS)
        message(STATUS "  Components: ${Modula_FIND_COMPONENTS}")
    endif()
endif()

if(NOT COMMAND declare_module)
    message(FATAL_ERROR "declare_module function not available")
endif()
