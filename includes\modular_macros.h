/**
 * @file modular_macros.h
 * @brief ModularFramework 宏定义系统
 *
 * 提供符合README.md规范的现代化模块注册宏定义：
 * 1. MODULAR_REGISTER_MODULE - 基础模块注册宏
 * 2. MODULAR_REGISTER_MODULE_EXTENDED - 扩展模块注册宏
 * 3. MODULAR_REGISTER_MODULE_WITH_DEPS - 带依赖的模块注册宏
 *
 * 这些宏实现了README.md中条件4的要求：
 * "主模块类需要在编译期或运行时显式注册(通过modular.registry)"
 *
 * **特性：**
 * - 编译期类型安全检查和概念约束验证
 * - 异常安全的注册机制和错误处理
 * - 支持现代C++20/23特性和最佳实践
 * - 详细的编译期诊断和错误信息
 * - 自动生成唯一的注册器标识符
 *
 * @version 3.0.0
 * <AUTHOR> Team
 */

#pragma once

// 防止重复包含和宏冲突
#ifndef MODULAR_FRAMEWORK_MACROS_H
#define MODULAR_FRAMEWORK_MACROS_H

/**
 * @brief 基础模块注册宏 - 现代化版本
 *
 * 实现README.md中条件4的基础模块注册功能
 * 用于注册模块基本元数据，符合模块定义规范
 * 提供编译期类型安全检查和异常安全的注册机制
 *
 * @param ModuleClass 模块类名（必须符合Module概念约束）
 * @param name 模块名称（字符串字面量，必须唯一且非空）
 * @param version 模块版本（字符串字面量，建议使用语义化版本）
 * @param description 模块描述（字符串字面量，可以为空）
 * @param priority 模块优先级（整数，0-100，数值越高优先级越高）
 *
 * @example
 * ```cpp
 * import modular.concepts;
 *
 * class MyModule {
 * public:
 *     bool initialize() { return true; }
 *     void shutdown() {}
 *     bool is_initialized() const { return true; }
 * };
 *
 * MODULAR_REGISTER_MODULE(MyModule, "MyModule", "1.0.0", "示例模块", 50)
 * ```
 */
#define MODULAR_REGISTER_MODULE(ModuleClass, name, version, description, priority) \
    namespace modular_framework_detail { \
        struct ModuleClass##_Registrar { \
            ModuleClass##_Registrar() noexcept { \
                /* 编译期类型安全检查 */ \
                static_assert(::modular::Module<ModuleClass>, \
                    "Error: " #ModuleClass " must satisfy the Module concept. " \
                    "Ensure it has initialize(), shutdown(), and is_initialized() methods."); \
                \
                /* 编译期参数验证 */ \
                static_assert(sizeof(name) > 1, \
                    "Error: Module name cannot be empty"); \
                static_assert(sizeof(version) > 1, \
                    "Error: Module version cannot be empty"); \
                static_assert(priority >= 0 && priority <= 100, \
                    "Error: Module priority must be between 0 and 100"); \
                \
                /* 异常安全的注册 */ \
                try { \
                    ::modular::register_module_info<ModuleClass>(name, version, description, priority); \
                } catch (...) { \
                    /* 静默处理注册失败，不影响程序启动 */ \
                } \
            } \
        }; \
        [[maybe_unused]] static const ModuleClass##_Registrar ModuleClass##_registrar_instance_; \
    }

/**
 * @brief 扩展模块注册宏 - 现代化版本
 *
 * 用于注册完整模块元数据的宏系统，支持所有配置选项
 * 提供编译期验证、异常安全和详细的错误诊断
 *
 * @param ModuleClass 模块类名（必须符合Module概念约束）
 * @param name 模块名称（字符串字面量）
 * @param version 模块版本（字符串字面量）
 * @param description 模块描述（字符串字面量）
 * @param priority 模块优先级（0-100）
 * @param author 作者信息（字符串字面量）
 * @param license 许可证信息（字符串字面量）
 * @param hot_reload 是否支持热重载（布尔值）
 * @param thread_safe 是否线程安全（布尔值）
 * @param timeout_ms 初始化超时时间（毫秒，正整数）
 * @param memory_mb 内存限制（MB，非负整数）
 *
 * @example
 * ```cpp
 * MODULAR_REGISTER_MODULE_EXTENDED(TestModule, "TestModule", "1.0.0", "Test module", 50,
 *                                 "Author", "MIT", false, true, 5000, 100)
 * ```
 */
#define MODULAR_REGISTER_MODULE_EXTENDED(ModuleClass, name, version, description, priority, \
                                        author, license, hot_reload, thread_safe, timeout_ms, memory_mb) \
    namespace modular_framework_detail { \
        struct ModuleClass##_ExtendedRegistrar { \
            ModuleClass##_ExtendedRegistrar() noexcept { \
                /* 编译期类型和概念检查 */ \
                static_assert(::modular::Module<ModuleClass>, \
                    "Error: " #ModuleClass " must satisfy the Module concept"); \
                \
                /* 编译期参数验证 */ \
                static_assert(sizeof(name) > 1, "Error: Module name cannot be empty"); \
                static_assert(sizeof(version) > 1, "Error: Module version cannot be empty"); \
                static_assert(priority >= 0 && priority <= 100, \
                    "Error: Module priority must be between 0 and 100"); \
                static_assert(timeout_ms > 0, \
                    "Error: Timeout must be positive"); \
                static_assert(memory_mb >= 0, \
                    "Error: Memory limit cannot be negative"); \
                \
                /* 异常安全的扩展注册 */ \
                try { \
                    ::modular::info::attributes info_attr{}; \
                    info_attr.name = name; \
                    info_attr.version = version; \
                    info_attr.description = description; \
                    info_attr.priority = priority; \
                    info_attr.author = author; \
                    info_attr.license = license; \
                    info_attr.hot_reload_supported = hot_reload; \
                    info_attr.thread_safe = thread_safe; \
                    info_attr.init_timeout_ms = timeout_ms; \
                    info_attr.memory_limit_mb = memory_mb; \
                    ::modular::register_module_info_extended<ModuleClass>(info_attr); \
                } catch (...) { \
                    /* 静默处理注册失败 */ \
                } \
            } \
        }; \
        [[maybe_unused]] static const ModuleClass##_ExtendedRegistrar ModuleClass##_extended_registrar_instance_; \
    }

/**
 * @brief 带依赖的模块注册宏 - 新增功能
 *
 * 用于注册带有明确依赖关系的模块，支持编译期依赖验证
 * 提供更强的类型安全和依赖关系检查
 *
 * @param ModuleClass 模块类名（必须符合Module概念约束）
 * @param name 模块名称（字符串字面量）
 * @param version 模块版本（字符串字面量）
 * @param description 模块描述（字符串字面量）
 * @param priority 模块优先级（0-100）
 * @param ... 依赖的模块名称列表（字符串字面量）
 *
 * @example
 * ```cpp
 * MODULAR_REGISTER_MODULE_WITH_DEPS(DatabaseModule, "DatabaseModule", "1.0.0",
 *                                   "Database access module", 80, "LoggingModule", "ConfigModule")
 * ```
 */
#define MODULAR_REGISTER_MODULE_WITH_DEPS(ModuleClass, name, version, description, priority, ...) \
    namespace modular_framework_detail { \
        struct ModuleClass##_DependencyRegistrar { \
            ModuleClass##_DependencyRegistrar() noexcept { \
                /* 编译期类型检查 */ \
                static_assert(::modular::Module<ModuleClass>, \
                    "Error: " #ModuleClass " must satisfy the Module concept"); \
                \
                /* 编译期参数验证 */ \
                static_assert(sizeof(name) > 1, "Error: Module name cannot be empty"); \
                static_assert(sizeof(version) > 1, "Error: Module version cannot be empty"); \
                static_assert(priority >= 0 && priority <= 100, \
                    "Error: Module priority must be between 0 and 100"); \
                \
                /* 异常安全的依赖注册 */ \
                try { \
                    ::modular::info::attributes info_attr{}; \
                    info_attr.name = name; \
                    info_attr.version = version; \
                    info_attr.description = description; \
                    info_attr.priority = priority; \
                    \
                    /* 设置依赖关系 */ \
                    const std::vector<std::string_view> deps = {__VA_ARGS__}; \
                    info_attr.dependencies.reserve(deps.size()); \
                    for (const auto& dep : deps) { \
                        info_attr.dependencies.emplace_back(dep); \
                    } \
                    \
                    ::modular::register_module_info_extended<ModuleClass>(info_attr); \
                } catch (...) { \
                    /* 静默处理注册失败 */ \
                } \
            } \
        }; \
        [[maybe_unused]] static const ModuleClass##_DependencyRegistrar ModuleClass##_dependency_registrar_instance_; \
    }

/**
 * @brief 向后兼容的宏别名
 *
 * 为了保持向后兼容性，提供旧宏名称的别名
 * 建议新代码使用MODULAR_前缀的新宏名称
 */
#define REGISTER_MODULE(...) MODULAR_REGISTER_MODULE(__VA_ARGS__)
#define REGISTER_MODULE_EXTENDED(...) MODULAR_REGISTER_MODULE_EXTENDED(__VA_ARGS__)

/**
 * @brief 编译期模块验证宏
 *
 * 用于在编译期验证模块类是否满足所有要求
 * 可以在开发阶段用于快速检查模块定义的正确性
 *
 * @param ModuleClass 要验证的模块类名
 */
#define MODULAR_VERIFY_MODULE(ModuleClass) \
    static_assert(::modular::Module<ModuleClass>, \
        "Error: " #ModuleClass " does not satisfy the Module concept. " \
        "Please ensure it has the required methods: initialize(), shutdown(), is_initialized()"); \
    static_assert(std::is_default_constructible_v<ModuleClass>, \
        "Error: " #ModuleClass " must be default constructible"); \
    static_assert(std::is_destructible_v<ModuleClass>, \
        "Error: " #ModuleClass " must be destructible"); \
    static_assert(!std::is_abstract_v<ModuleClass>, \
        "Error: " #ModuleClass " cannot be an abstract class")

/**
 * @brief 模块信息获取宏
 *
 * 用于在编译期获取模块的基本信息
 * 主要用于调试和开发工具
 *
 * @param ModuleClass 模块类名
 */
#define MODULAR_GET_MODULE_NAME(ModuleClass) \
    ::modular::module_traits<ModuleClass>::get_name()

#define MODULAR_GET_MODULE_VERSION(ModuleClass) \
    ::modular::module_traits<ModuleClass>::get_version()

#define MODULAR_GET_MODULE_PRIORITY(ModuleClass) \
    ::modular::module_traits<ModuleClass>::get_priority()

#endif // MODULAR_FRAMEWORK_MACROS_H
