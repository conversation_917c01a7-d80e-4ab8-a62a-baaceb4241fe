/**
 * @file testmoduleb_impl.cpp
 * @brief TestModuleB implementation file to generate library
 */

#include "testmoduleb_module.h"

// This file exists to ensure TestModuleB generates a library file
// The actual implementation is in the header file (header-only design)

namespace {
// Force library generation with a dummy symbol
void testmoduleb_force_lib_generation() {
    // This function ensures the library is generated
    // even though TestModuleB is header-only
}
}
