# ==============================================================================
# ModulaInstall.cmake - Installation Configuration
# ==============================================================================
#
# This module handles all installation-related configuration for the Modula Framework.
# It manages target installation, file installation, and package configuration.

cmake_minimum_required(VERSION 3.28)
include_guard(GLOBAL)

# ==============================================================================
# Installation Configuration Functions
# ==============================================================================

#[=======================================================================[.rst:
.. command:: modula_install_targets

  Install Modula Framework targets.
#]=======================================================================]
function(modula_install_targets)
    if(NOT MODULA_INSTALL)
        return()
    endif()

    # Install main library target
    if(TARGET modula)
        install(TARGETS modula
            EXPORT ModulaTargets
            LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
            ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
            RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
            FILE_SET CXX_MODULES DESTINATION ${CMAKE_INSTALL_LIBDIR}/modules/modula
            FILE_SET HEADERS DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
            INCLUDES DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
        )
    endif()
endfunction()

#[=======================================================================[.rst:
.. command:: modula_install_development_files

  Install header files and development resources.

  This function installs public header files, CMake modules, and the code generator tool.
#]=======================================================================]
function(modula_install_development_files)
    if(NOT MODULA_INSTALL)
        return()
    endif()

    # Install public header files
    install(DIRECTORY includes/
        DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/modula
        FILES_MATCHING
        PATTERN "*.h"
        PATTERN "*.hpp"
    )

    # Install CMake modules for downstream projects
    install(DIRECTORY cmake/processor
        DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/Modula/modules
    )
    install(FILES
        cmake/ModulaCompiler.cmake
        cmake/ModulaConfiguration.cmake
        cmake/ModulaDeclaration.cmake
        cmake/ModulaPreprocessor.cmake
        cmake/ModulaUtils.cmake
        DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/Modula/modules
    )

    # Install the Python code generator tool
    # The empty 'scripts' directory might cause issues if not existing, ensure it's handled gracefully.
    install(DIRECTORY tools/modula_generator/
        DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/Modula/tools/modula_generator
        PATTERN "scripts/env_manager.pyc" EXCLUDE
        PATTERN "__pycache__" EXCLUDE
    )

    message(STATUS "Modula: Development files configured for installation")
endfunction()

# ==============================================================================
# Package Configuration
# ==============================================================================

#[=======================================================================[.rst:
.. command:: modula_install_package_config

  Install CMake package configuration files.

  This function generates and installs the CMake package configuration
  files needed for find_package() support.
#]=======================================================================]
function(modula_install_package_config)
    if(NOT MODULA_INSTALL)
        return()
    endif()

    # Install targets export file
    install(EXPORT ModulaTargets
        FILE ModulaTargets.cmake
        NAMESPACE Modula::
        DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/Modula
    )

    # Generate package version file
    write_basic_package_version_file(
        "${CMAKE_CURRENT_BINARY_DIR}/ModulaConfigVersion.cmake"
        VERSION ${PROJECT_VERSION}
        COMPATIBILITY SameMajorVersion
    )

    # Generate package configuration file
    configure_package_config_file(
        "${CMAKE_CURRENT_SOURCE_DIR}/cmake/ModulaConfig.cmake.in"
        "${CMAKE_CURRENT_BINARY_DIR}/ModulaConfig.cmake"
        INSTALL_DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/Modula
        PATH_VARS CMAKE_INSTALL_LIBDIR CMAKE_INSTALL_INCLUDEDIR
    )

    # Install package configuration files
    install(FILES
        "${CMAKE_CURRENT_BINARY_DIR}/ModulaConfig.cmake"
        "${CMAKE_CURRENT_BINARY_DIR}/ModulaConfigVersion.cmake"
        DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/Modula
    )

    message(STATUS "Modula: Package configuration files configured for installation")
endfunction()

#[=======================================================================[.rst:
.. command:: modula_export_build_tree

  Export targets for build tree usage.

  This function exports targets for use from the build tree.
#]=======================================================================]
function(modula_export_build_tree)
    if(NOT MODULA_INSTALL)
        return()
    endif()

    # Export targets for build tree usage
    export(EXPORT ModulaTargets
        FILE "${CMAKE_CURRENT_BINARY_DIR}/ModulaTargets.cmake"
        NAMESPACE Modula::
    )

    configure_file(
        "${CMAKE_CURRENT_SOURCE_DIR}/cmake/ModulaConfig.cmake.in"
        "${CMAKE_CURRENT_BINARY_DIR}/ModulaConfig.cmake"
        @ONLY
    )

    # Register package in user package registry
    export(PACKAGE Modula)

    message(STATUS "Modula: Build tree export configured")
endfunction()

# ==============================================================================
# Installation Configuration
# ==============================================================================

# Execute installation configuration if enabled
if(MODULA_INSTALL)
    modula_install_targets()
    modula_install_development_files()
    modula_install_package_config()
    modula_export_build_tree()

    message(STATUS "Modula: Installation configuration completed")
endif()
